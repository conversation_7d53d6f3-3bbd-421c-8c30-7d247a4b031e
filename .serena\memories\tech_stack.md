# Tech Stack - RecLand

## Backend Framework
- **Laravel 9.x** - PHP Framework chính
- **PHP 8.0.2+** - Ngôn ngữ lập trình

## Frontend
- **Vue.js 3.2.36** - JavaScript Framework
- **Bootstrap** - CSS Framework
- **jQuery 3.7.1** - JavaScript Library
- **SASS/SCSS** - CSS Preprocessor
- **Laravel Mix** - Asset compilation

## Database
- **MySQL** - Database chính
- **Redis** - Cache và Session storage

## Key Laravel Packages
- **Backpack CRUD 5.6** - Admin panel
- **Laravel Sanctum** - API authentication
- **Laravel Socialite** - Social login
- **Yajra DataTables** - Server-side tables
- **Maatwebsite Excel** - Excel import/export
- **DomPDF** - PDF generation
- **Laravel Auditing** - Model auditing
- **Spatie Visitor** - Visit tracking

## Development Tools
- **<PERSON><PERSON> Mix** - Asset compilation
- **PHPUnit** - Testing framework
- **Laravel Debugbar** - Development debugging
- **Faker** - Test data generation

## External Services
- **AWS S3** - File storage
- **ZaloPay** - Payment gateway
- **reCAPTCHA** - Bot protection

## Architecture Patterns
- **Repository Pattern** - Data access layer
- **Service Pattern** - Business logic layer
- **Observer Pattern** - Model events
- **Job Queue** - Background processing