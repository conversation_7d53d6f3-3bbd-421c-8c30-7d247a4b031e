# Business Logic - RecLand

## Core Business Models

### 1. User Management
- **User**: Ngư<PERSON>i dùng cơ bản
- **Employer**: <PERSON><PERSON><PERSON> tuyển dụng
- **Collaborator/Rec**: C<PERSON>ng tác viên môi giới CV
- **Admin**: <PERSON><PERSON><PERSON>n trị viên

### 2. Job & CV Flow
```
Job Creation → CV Submission → Interview → Onboard → Payment
```

### 3. Two Main Services

#### MarketCV Service
- CV được bán trong kho (WareHouseCv)
- Employer mua CV từ kho
- Cộng tác viên kiếm hoa hồng

#### Submit CV Service  
- CV được nộp trực tiếp cho Job
- Quy trình: Submit → Interview → Onboard
- Payment theo từng giai đoạn

### 4. Payment System
- **Wallet**: Ví điện tử cho users
- **Transaction**: <PERSON>ia<PERSON> dịch
- **Deposit**: <PERSON><PERSON><PERSON> tiền
- **ZaloPay**: <PERSON><PERSON>ng <PERSON><PERSON> toán

### 5. Status Flow
```
Submit CV → Pending → Interview → Pass/Fail → Onboard → Success/Cancel
```

### 6. Key Business Rules
- Mỗi service có 3 forms: CV Data, Interview, Onboard
- Payment theo milestone
- Bonus system cho collaborators
- Complaint handling system
- Real/Fake data filtering (is_real field)

### 7. Data Categorization
- **warehouse_cvs**: 
  - source = null: Collaborator CV
  - source != null: Internal CV
- **is_real field**: Phân biệt data thật/giả

### 8. Notification System
- Email notifications cho các events
- In-app notifications
- SMS notifications (optional)

### 9. Reporting & Statistics
- Admin statistics với filtering
- Revenue tracking
- User activity monitoring
- CV source analysis