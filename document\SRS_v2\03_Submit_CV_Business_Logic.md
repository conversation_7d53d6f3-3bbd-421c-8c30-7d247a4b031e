# 03. Submit CV Business Logic

## 1. Tổng quan Business Logic

### 1.1. Core Business Rules

-   CTV chỉ c<PERSON> thể submit CV từ kho của mình
-   Mỗi CV chỉ được submit 1 lần cho 1 job
-   Candidate phải xác nhận trong 48h
-   Business logic khác nhau hoàn toàn theo bonus_type
-   Commission và workflow phụ thuộc vào bonus_type

### 1.2. Key Business Entities

-   **CTV (Cộng tác viên)**: Người giới thiệu ứng viên
-   **NTD (Nhà tuyển dụng)**: Người tuyển dụng
-   **Candidate**: Ứng viên được giới thiệu
-   **Admin**: Quản trị viên hệ thống

### 1.3. Bonus Type Classification

Submit CV workflow được chia thành 3 hình thức chính dựa trên `job.bonus_type`:

#### 1.3.1. CV Type (`bonus_type = 'cv'`)

-   **<PERSON><PERSON>c đích**: NTD mua thông tin CV để liên hệ trực tiếp
-   **Payment**: Thanh toán ngay khi NTD accept
-   **Commission**: 100% sau 7 ngày không khiếu nại
-   **Workflow**: Submit → Accept → Buy CV → Payment

#### 1.3.2. Interview Type (`bonus_type = 'interview'`)

-   **Mục đích**: NTD tuyển dụng qua phỏng vấn
-   **Payment**: Thanh toán dựa trên kết quả phỏng vấn
-   **Commission**: 100% sau 24h nếu pass interview
-   **Workflow**: Submit → Accept → Interview → Payment

#### 1.3.3. Onboard Type (`bonus_type = 'onboard'`)

-   **Mục đích**: NTD tuyển dụng với cam kết onboard
-   **Payment**: Thanh toán theo giai đoạn
-   **Commission**: 15% + 10% + 75% theo timeline
-   **Workflow**: Submit → Accept → Interview → Onboard → Trial → Success

## 2. CV Type Business Logic (`bonus_type = 'cv'`)

### 2.1. CV Type Workflow Overview

CV Type là hình thức đơn giản nhất, NTD chỉ cần mua thông tin CV để liên hệ trực tiếp với ứng viên.

### 2.2. CV Type State Flow

```mermaid
stateDiagram-v2
    [*] --> PendingConfirm : CTV submit CV
    PendingConfirm --> CandidateRejected : Candidate từ chối (48h)
    PendingConfirm --> WaitingPayment : Candidate xác nhận

    WaitingPayment --> BuyCVDataSuccessful : NTD thanh toán
    WaitingPayment --> RecruiterRejectCV : NTD từ chối

    BuyCVDataSuccessful --> [*] : Hoàn thành
    RecruiterRejectCV --> [*] : Kết thúc
    CandidateRejected --> [*] : Kết thúc
```

### 2.3. CV Type Status Definitions

| Status ID | Name                   | Description           | Business Logic                   |
| --------- | ---------------------- | --------------------- | -------------------------------- |
| 9         | Pending confirm        | Chờ ứng viên xác nhận | Candidate có 48h để xác nhận     |
| 21        | Waiting Payment        | Chờ NTD thanh toán    | NTD quyết định mua CV hay không  |
| 18        | Buy CV data successful | Mua CV thành công     | NTD đã thanh toán, có thể xem CV |
| 22        | Recruiter Reject CV    | NTD từ chối CV        | NTD không mua CV                 |
| 12        | Candidate rejected     | Ứng viên từ chối      | Auto sau 48h không xác nhận      |

### 2.4. CV Type Business Rules

#### 2.4.1. Submit CV Validation

```php
public function validateCvTypeSubmit($jobId, $warehouseCvId, $ctvId): bool
{
    $job = Job::find($jobId);

    // Kiểm tra bonus_type
    if ($job->bonus_type !== 'cv') {
        throw new Exception('Job không phải CV type');
    }

    // Kiểm tra duplicate submit
    $existingSubmit = SubmitCv::where('job_id', $jobId)
        ->where('warehouse_cv_id', $warehouseCvId)
        ->where('user_id', $ctvId)
        ->exists();

    if ($existingSubmit) {
        throw new Exception('CV đã được submit cho job này');
    }

    return true;
}
```

#### 2.4.2. Payment Processing

```php
public function processCvPayment($submitCvId): bool
{
    $submitCv = SubmitCv::find($submitCvId);
    $employer = $submitCv->employer;
    $bonus = $submitCv->bonus_ntd;

    // Kiểm tra balance
    if ($employer->wallet->amount < $bonus) {
        throw new Exception('Không đủ số dư');
    }

    DB::transaction(function() use ($submitCv, $employer, $bonus) {
        // Trừ tiền NTD
        $employer->wallet->subtractAmount($bonus, $submitCv, 'Mua CV data');

        // Cập nhật status
        $submitCv->update([
            'status' => 18, // Buy CV data successful
            'status_payment' => 1, // Đã thanh toán
            'date_change_status' => now()
        ]);

        // Log payment history
        SubmitCvHistoryPayment::create([
            'user_id' => $employer->id,
            'submit_cv_id' => $submitCv->id,
            'type' => 0, // Trừ tiền
            'amount' => $bonus,
            'balance' => $employer->wallet->amount,
            'type_of_sale' => 'cv'
        ]);

        // Schedule payment to CTV after 7 days
        RecSumPointSubmit::dispatch($submitCv->id)
            ->delay(now()->addMinutes(7 * 24 * 60));
    });

    return true;
}
```

### 2.5. CV Type Commission Logic

#### 2.5.1. Commission Calculation

```php
public function calculateCvCommission($submitCv): int
{
    $baseCommission = $submitCv->bonus_ntd;

    // Nếu có ủy quyền, CTV chỉ nhận 20%
    if ($submitCv->authorize === 1) {
        return $baseCommission * 0.2;
    }

    return $baseCommission;
}
```

#### 2.5.2. Commission Payment (RecSumPointSubmit Job)

```php
// Job được dispatch sau 7 ngày
public function handle()
{
    $submitCv = SubmitCv::find($this->submitCvId);

    // Chỉ thanh toán cho CV type
    if ($submitCv->bonus_type !== 'cv') {
        return;
    }

    // Kiểm tra điều kiện thanh toán
    if ($submitCv->status === 18 && // Buy CV data successful
        ($submitCv->status_complain === 0 || $submitCv->status_complain === 5)) {

        $commission = $this->calculateCvCommission($submitCv);

        // Cộng tiền cho CTV
        $submitCv->rec->wallet->addAmount($commission, $submitCv, 'Commission CV');

        // Cập nhật status payment
        $submitCv->update(['status_payment' => 2]); // Đã thanh toán
    }
}
```

### 2.6. CV Type Timeout Scenarios

#### 2.6.1. Candidate Confirmation Timeout

```php
// RejectRecruitmentSubmit job - 48h delay
if ($submitCv->status === 9 && $submitCv->bonus_type === 'cv') {
    $submitCv->update([
        'status' => 12, // Candidate rejected
        'date_change_status' => now()
    ]);
}
```

### 2.7. CV Type Error Handling

#### 2.7.1. Payment Failures

```php
try {
    $this->processCvPayment($submitCvId);
} catch (InsufficientBalanceException $e) {
    // Thông báo NTD nạp thêm tiền
    $submitCv->employer->notify(new InsufficientBalanceNotification());
} catch (Exception $e) {
    // Log error và thông báo admin
    Log::error('CV payment failed', ['submit_cv_id' => $submitCvId, 'error' => $e->getMessage()]);
}
```

## 3. Interview Type Business Logic (`bonus_type = 'interview'`)

### 3.1. Interview Type Workflow Overview

Interview Type yêu cầu NTD phỏng vấn ứng viên và thanh toán dựa trên kết quả phỏng vấn.

### 3.2. Interview Type State Flow

```mermaid
stateDiagram-v2
    [*] --> PendingConfirm : CTV submit CV
    PendingConfirm --> CandidateRejected : Candidate từ chối (48h)
    PendingConfirm --> WaitingPayment : Candidate xác nhận

    WaitingPayment --> WaitingSetupInterview : NTD thanh toán
    WaitingPayment --> RecruiterRejectCV : NTD từ chối

    WaitingSetupInterview --> WaitingConfirmCalendar : NTD đặt lịch PV
    WaitingConfirmCalendar --> WaitingInterview : CTV xác nhận (48h)
    WaitingConfirmCalendar --> RejectInterviewSchedule : CTV từ chối/timeout

    WaitingInterview --> PassInterview : PV thành công
    WaitingInterview --> FailInterview : PV thất bại

    PassInterview --> [*] : Hoàn thành
    FailInterview --> [*] : Kết thúc
    RejectInterviewSchedule --> [*] : Kết thúc
    RecruiterRejectCV --> [*] : Kết thúc
    CandidateRejected --> [*] : Kết thúc
```

### 3.3. Interview Type Status Definitions

| Status ID | Name                      | Description           | Business Logic                      |
| --------- | ------------------------- | --------------------- | ----------------------------------- |
| 9         | Pending confirm           | Chờ ứng viên xác nhận | Candidate có 48h để xác nhận        |
| 21        | Waiting Payment           | Chờ NTD thanh toán    | NTD quyết định tuyển dụng hay không |
| 3         | Waiting setup interview   | Chờ NTD đặt lịch PV   | NTD đã thanh toán, cần đặt lịch     |
| 4         | Waiting confirm calendar  | Chờ CTV xác nhận lịch | CTV có 48h để xác nhận              |
| 7         | Waiting Interview         | Chờ phỏng vấn         | Lịch đã được xác nhận               |
| 8         | Pass Interview            | PV thành công         | Thanh toán commission cho CTV       |
| 10        | Fail Interview            | PV thất bại           | Hoàn tiền cho NTD                   |
| 5         | Reject Interview schedule | Từ chối lịch PV       | CTV từ chối hoặc timeout            |
| 22        | Recruiter Reject CV       | NTD từ chối CV        | NTD không tuyển dụng                |
| 12        | Candidate rejected        | Ứng viên từ chối      | Auto sau 48h không xác nhận         |

### 3.4. Interview Type Business Rules

#### 3.4.1. Payment Processing

```php
public function processInterviewPayment($submitCvId): bool
{
    $submitCv = SubmitCv::find($submitCvId);
    $employer = $submitCv->employer;
    $bonus = $submitCv->bonus_ntd;

    DB::transaction(function() use ($submitCv, $employer, $bonus) {
        // Trừ tiền NTD
        $employer->wallet->subtractAmount($bonus, $submitCv, 'Phí phỏng vấn');

        // Cập nhật status
        $submitCv->update([
            'status' => 3, // Waiting setup interview
            'status_payment' => 1, // Đã thanh toán
            'date_change_status' => now()
        ]);

        // Log payment history
        SubmitCvHistoryPayment::create([
            'user_id' => $employer->id,
            'submit_cv_id' => $submitCv->id,
            'type' => 0, // Trừ tiền
            'amount' => $bonus,
            'balance' => $employer->wallet->amount,
            'type_of_sale' => 'interview'
        ]);
    });

    return true;
}
```

#### 3.4.2. Interview Booking Logic

```php
public function bookInterview($submitCvId, $bookingData): bool
{
    $submitCv = SubmitCv::find($submitCvId);

    // Validate status
    if (!in_array($submitCv->status, [3, 5])) { // Waiting setup hoặc Reject schedule
        throw new Exception('Không thể đặt lịch phỏng vấn');
    }

    $book = SubmitCvBook::create([
        'ntd_id' => $submitCv->employer->id,
        'ctv_id' => $submitCv->user_id,
        'submit_cvs_id' => $submitCv->id,
        'date_book' => $bookingData['date_book'],
        'time_book' => $bookingData['time_book'],
        'address' => $bookingData['address'],
        'phone' => $bookingData['phone'],
        'name' => $bookingData['name'],
        'status' => 0 // Vừa đặt
    ]);

    // Cập nhật submit CV status
    $submitCv->update([
        'status' => 4, // Waiting confirm calendar
        'date_change_status' => now()
    ]);

    // Schedule auto-reject nếu CTV không xác nhận trong 48h
    RejectBookExpireSubmit::dispatch($book, $submitCv->employer)
        ->delay(now()->addHours(48));

    return true;
}
```

#### 3.4.3. Interview Confirmation Logic

```php
public function confirmInterview($bookId, $action): bool
{
    $book = SubmitCvBook::find($bookId);
    $submitCv = $book->submitCv;

    if ($action === 'confirm') {
        $book->update(['status' => 1]); // Đã xác nhận
        $submitCv->update([
            'status' => 7, // Waiting Interview
            'date_change_status' => now()
        ]);

        // Schedule reminder email
        SendemailBookSubmit::dispatch($submitCv, $submitCv->rec, $book)
            ->delay($book->date_book->subDay());

    } else {
        $book->update(['status' => 2]); // Từ chối
        $submitCv->update([
            'status' => 5, // Reject Interview schedule
            'date_change_status' => now()
        ]);

        // Hoàn tiền cho NTD
        $this->refundInterviewPayment($submitCv);
    }

    return true;
}
```

### 3.5. Interview Type Commission Logic

#### 3.5.1. Commission Payment (PayInterviewSubmit Job)

```php
// Job được dispatch sau 24h khi pass interview
public function handle()
{
    $submitCv = SubmitCv::find($this->submitCvId);

    // Kiểm tra điều kiện thanh toán
    if (($submitCv->status === 8 || $submitCv->status === 10) && // Pass/Fail Interview
        ($submitCv->status_complain === 0 || $submitCv->status_complain === 5)) {

        if ($submitCv->status === 8) { // Pass Interview
            $commission = $submitCv->bonus_ntd;

            // Cộng tiền cho CTV
            $submitCv->rec->wallet->addAmount($commission, $submitCv, 'Commission Interview');

            // Cập nhật status payment
            $submitCv->update(['status_payment' => 2]); // Đã thanh toán
        }
    }
}
```

### 3.6. Interview Type Timeout Scenarios

#### 3.6.1. Interview Confirmation Timeout (RejectBookExpireSubmit)

```php
// Job được dispatch sau 48h khi đặt lịch
public function handle()
{
    if ($this->book->status === 0) { // Chưa xác nhận
        // Auto reject
        $this->book->update(['status' => 2]);

        $submitCv = $this->book->submitCv;
        $submitCv->update(['status' => 5]); // Reject Interview schedule

        // Hoàn tiền cho NTD
        $this->refundInterviewPayment($submitCv);
    }
}
```

## 4. Onboard Type Business Logic (`bonus_type = 'onboard'`)

### 4.1. Onboard Type Workflow Overview

Onboard Type là hình thức phức tạp nhất, yêu cầu quy trình đầy đủ từ phỏng vấn đến onboard và trial work.

### 4.2. Onboard Type State Flow

```mermaid
stateDiagram-v2
    [*] --> PendingConfirm : CTV submit CV
    PendingConfirm --> CandidateRejected : Candidate từ chối (48h)
    PendingConfirm --> WaitingPayment : Candidate xác nhận

    WaitingPayment --> WaitingSetupInterview : NTD thanh toán cọc
    WaitingPayment --> RecruiterRejectCV : NTD từ chối

    WaitingSetupInterview --> WaitingConfirmCalendar : NTD đặt lịch PV
    WaitingConfirmCalendar --> WaitingInterview : CTV xác nhận (48h)
    WaitingConfirmCalendar --> RejectInterviewSchedule : CTV từ chối/timeout

    WaitingInterview --> PassInterview : PV thành công
    WaitingInterview --> FailInterview : PV thất bại

    PassInterview --> Offering : NTD offer
    Offering --> Onboarded : Candidate chấp nhận
    Offering --> CandidateRejected : Candidate từ chối

    Onboarded --> TrialWork : Sau 7 ngày
    TrialWork --> SuccessRecruitment : Sau 67 ngày
    TrialWork --> FailTrialWork : NTD đánh giá fail

    SuccessRecruitment --> [*] : Hoàn thành
    FailTrialWork --> [*] : Kết thúc
    FailInterview --> [*] : Kết thúc
    RejectInterviewSchedule --> [*] : Kết thúc
```

### 4.3. Onboard Type Status Definitions

| Status ID | Name                      | Description            | Business Logic                      |
| --------- | ------------------------- | ---------------------- | ----------------------------------- |
| 9         | Pending confirm           | Chờ ứng viên xác nhận  | Candidate có 48h để xác nhận        |
| 21        | Waiting Payment           | Chờ NTD thanh toán cọc | NTD quyết định tuyển dụng hay không |
| 3         | Waiting setup interview   | Chờ NTD đặt lịch PV    | NTD đã thanh toán cọc               |
| 4         | Waiting confirm calendar  | Chờ CTV xác nhận lịch  | CTV có 48h để xác nhận              |
| 7         | Waiting Interview         | Chờ phỏng vấn          | Lịch đã được xác nhận               |
| 8         | Pass Interview            | PV thành công          | Chuyển sang offering                |
| 10        | Fail Interview            | PV thất bại            | Hoàn cọc cho NTD                    |
| 11        | Offering                  | Đang offer             | Chờ candidate quyết định            |
| 13        | Onboarded                 | Đã onboard             | Bắt đầu làm việc                    |
| 14        | Trial work                | Thử việc               | Giai đoạn thử việc 60 ngày          |
| 16        | Success Recruitment       | Thành công             | Hoàn thành tuyển dụng               |
| 17        | Fail trial work           | Thử việc thất bại      | NTD đánh giá không đạt              |
| 5         | Reject Interview schedule | Từ chối lịch PV        | CTV từ chối hoặc timeout            |
| 22        | Recruiter Reject CV       | NTD từ chối CV         | NTD không tuyển dụng                |
| 12        | Candidate rejected        | Ứng viên từ chối       | Auto sau 48h hoặc từ chối offer     |

### 4.4. Onboard Type Business Rules

#### 4.4.1. Deposit Payment Processing

```php
public function processOnboardDeposit($submitCvId): bool
{
    $submitCv = SubmitCv::find($submitCvId);
    $employer = $submitCv->employer;
    $deposit = $submitCv->bonus_ntd * 0.1; // 10% cọc

    DB::transaction(function() use ($submitCv, $employer, $deposit) {
        // Trừ tiền cọc NTD
        $employer->wallet->subtractAmount($deposit, $submitCv, 'Cọc onboard');

        // Cập nhật status
        $submitCv->update([
            'status' => 3, // Waiting setup interview
            'status_payment' => 1, // Đã thanh toán cọc
            'date_change_status' => now()
        ]);

        // Log payment history
        SubmitCvHistoryPayment::create([
            'user_id' => $employer->id,
            'submit_cv_id' => $submitCv->id,
            'type' => 0, // Trừ tiền
            'amount' => $deposit,
            'balance' => $employer->wallet->amount,
            'type_of_sale' => 'onboard'
        ]);
    });

    return true;
}
```

#### 4.4.2. Onboard Scheduling Logic

```php
public function scheduleOnboard($submitCvId, $onboardData): bool
{
    $submitCv = SubmitCv::find($submitCvId);

    // Validate status - chỉ sau khi pass interview
    if ($submitCv->status !== 8) { // Pass Interview
        throw new Exception('Chưa pass interview');
    }

    $onboard = SubmitCvOnboard::create([
        'ntd_id' => $submitCv->employer->id,
        'ctv_id' => $submitCv->user_id,
        'submit_cvs_id' => $submitCv->id,
        'date_book' => $onboardData['date_book'],
        'time_book' => $onboardData['time_book'],
        'address' => $onboardData['address'],
        'name' => $onboardData['name'],
        'status' => 0 // Vừa đặt
    ]);

    // Cập nhật submit CV status
    $submitCv->update([
        'status' => 13, // Onboarded
        'date_change_status' => now()
    ]);

    // Schedule trial work transition sau 7 ngày
    ChangeToTrailWorkSubmit::dispatch($submitCv->id, $submitCv->employer)
        ->delay(now()->addDays(7));

    return true;
}
```

### 4.5. Onboard Type Commission Logic

#### 4.5.1. Commission Payment Schedule (PayOnboardSubmit Jobs)

```php
// ChangeToTrailWorkSubmit job - sau 7 ngày onboard
public function handle()
{
    $submitCv = SubmitCv::find($this->submitCvId);

    // Chuyển sang trial work
    $submitCv->update([
        'status' => 14, // Trial work
        'date_change_status' => now()
    ]);

    // Setup payment schedule
    $onboard = $submitCv->onboard;
    $carbonDate = Carbon::parse($onboard->date_book);

    // 15% sau 30 ngày
    PayOnboardSubmit::dispatch($submitCv->id, 15)
        ->delay($carbonDate->addMinutes(30 * 24 * 60));

    // 10% sau 45 ngày
    PayOnboardSubmit::dispatch($submitCv->id, 10)
        ->delay($carbonDate->addMinutes(45 * 24 * 60));

    // 75% sau 67 ngày
    PayOnboardSubmit::dispatch($submitCv->id, 75)
        ->delay($carbonDate->addMinutes(67 * 24 * 60));

    // Auto success sau 67 ngày
    SuccessRecuitmentSubmit::dispatch($submitCv->id)
        ->delay($carbonDate->addMinutes(67 * 24 * 60));
}
```

#### 4.5.2. Commission Payment Logic

```php
// PayOnboardSubmit job
public function handle()
{
    $submitCv = SubmitCv::find($this->submitCvId);

    // Kiểm tra điều kiện
    if (($submitCv->status === 14 || $submitCv->status === 16) && // Trial work hoặc Success
        ($submitCv->status_complain === 0 || $submitCv->status_complain === 5)) {

        $totalCommission = $submitCv->bonus_ntd;
        $paymentAmount = ($totalCommission * $this->percent) / 100;

        // Cộng tiền cho CTV
        $submitCv->rec->wallet->addAmount($paymentAmount, $submitCv, "Commission Onboard {$this->percent}%");

        // Log payment
        SubmitCvHistoryPayment::create([
            'user_id' => $submitCv->user_id,
            'submit_cv_id' => $submitCv->id,
            'type' => 1, // Cộng tiền
            'percent' => $this->percent,
            'amount' => $paymentAmount,
            'balance' => $submitCv->rec->wallet->amount,
            'type_of_sale' => 'onboard'
        ]);
    }
}
```

### 4.6. Onboard Type Timeout Scenarios

#### 4.6.1. Auto Success (SuccessRecuitmentSubmit)

```php
// Job được dispatch sau 67 ngày từ onboard
public function handle()
{
    $submitCv = SubmitCv::find($this->submitCvId);

    // Kiểm tra vẫn ở trial work
    if ($submitCv->status === 14 && // Trial work
        ($submitCv->status_complain === 0 || $submitCv->status_complain === 5)) {

        // Auto chuyển thành công
        $submitCv->update([
            'status' => 16, // Success Recruitment
            'status_payment' => 4, // Hoàn thành
            'date_change_status' => now()
        ]);

        // Send notifications
        $this->sendSuccessNotifications($submitCv);
    }
}
```

## 5. Common Business Logic (Áp dụng cho tất cả bonus_type)

### 5.1. Complaint Handling

#### 5.1.1. Complaint Status Flow

```mermaid
stateDiagram-v2
    [*] --> NoComplaint : status_complain = 0
    NoComplaint --> WaitingCTVConfirm : NTD khiếu nại

    WaitingCTVConfirm --> CTVAccepted : CTV chấp nhận
    WaitingCTVConfirm --> WaitingAdminReview : CTV từ chối
    WaitingCTVConfirm --> CTVAccepted : Auto sau 7 ngày

    WaitingAdminReview --> AdminAccepted : Admin chấp nhận
    WaitingAdminReview --> ComplaintFailed : Admin từ chối
    WaitingAdminReview --> AdminAccepted : Auto sau 7 ngày

    CTVAccepted --> [*] : Hoàn tiền NTD
    AdminAccepted --> [*] : Hoàn tiền NTD
    ComplaintFailed --> [*] : Không hoàn tiền
```

#### 5.1.2. Complaint Business Rules

```php
public function canComplain($submitCv): bool
{
    // Chỉ khiếu nại được 1 lần
    if ($submitCv->count_complain >= 1) {
        return false;
    }

    // Phải trong trạng thái phù hợp
    $allowedStatuses = [18, 8, 16]; // Buy CV successful, Pass Interview, Success Recruitment

    return in_array($submitCv->status, $allowedStatuses);
}
```

### 5.2. Authorization Logic

#### 5.2.1. Authorization Types

```php
// authorize = 0: CTV tự quản lý
if ($submitCv->authorize === 0) {
    $submitCv->rec->notify(new Notification());
}

// authorize = 1: Admin quản lý thay CTV
if ($submitCv->authorize === 1) {
    Mail::to(config('settings.global.email_admin'))
        ->send(new AdminNotification());
}
```

### 5.3. Validation Rules

#### 5.3.1. Universal Validation

```php
public function validateSubmitCv($data): array
{
    return [
        'job_id' => 'required|exists:jobs,id',
        'warehouse_cv_id' => [
            'required',
            'exists:warehouse_cvs,id',
            new CheckDuplicateSubmitByCv($data['warehouse_cv_id'], $data['job_id'])
        ],
        'expected_date' => 'required|date|after:today',
        'authorize' => 'boolean',
    ];
}
```

#### 5.3.2. Status Transition Validation

```php
public function validateStatusTransition($oldStatus, $newStatus, $bonusType): bool
{
    $allowedTransitions = config("constant.status_transitions.{$bonusType}");

    return in_array($newStatus, $allowedTransitions[$oldStatus] ?? []);
}
```

---

**Next**: [04_Submit_CV_Jobs_Scheduler.md](./04_Submit_CV_Jobs_Scheduler.md)
| 7 | Draft | Nháp | 9 |
| 10 | Admin review | Admin review | 1, 11 |
| 11 | Admin rejected | Admin từ chối | - |
| 12 | Candidate rejected | Ứng viên từ chối | - |
| 14 | Trial work | Thử việc | 16, 17 |
| 16 | Success Recruitment | Thành công | - |
| 17 | Fail trial work | Thử việc thất bại | - |

## 3. Business Rules chi tiết

### 3.1. Submit CV Rules

#### 3.1.1. Validation Rules

```php
// Kiểm tra duplicate submit
if (SubmitCv::where('user_id', $ctvId)
    ->where('job_id', $jobId)
    ->where('warehouse_cv_id', $cvId)
    ->exists()) {
    throw new Exception('CV đã được submit cho job này');
}

// Kiểm tra CV thuộc về CTV
if ($wareHouseCv->user_id !== $ctvId) {
    throw new Exception('CV không thuộc về CTV này');
}

// Kiểm tra job còn active
if (!$job->is_active || $job->deleted_at) {
    throw new Exception('Job không còn active');
}

// Kiểm tra CTV có đủ điều kiện
if ($ctv->status !== 'active') {
    throw new Exception('CTV không active');
}
```

#### 3.1.2. Business Constraints

-   CTV chỉ submit được CV từ kho của mình
-   Không được submit CV đã expired
-   Job phải còn trong thời gian tuyển dụng
-   CTV phải có đủ balance nếu có phí

### 3.2. Authorization Logic

#### 3.2.1. Authorization Types

```php
// authorize = 0: CTV tự quản lý
if ($submitCv->authorize === 0) {
    // CTV tự xử lý tất cả notifications
    $submitCv->rec->notify(new Notification());
}

// authorize = 1: Admin quản lý thay CTV
if ($submitCv->authorize === 1) {
    // Admin nhận tất cả notifications
    Mail::to(config('settings.global.email_admin'))
        ->send(new AdminNotification());
}
```

#### 3.2.2. Authorization Status

-   `authorize_status = 0`: Chờ xác nhận
-   `authorize_status = 1`: Đồng ý ủy quyền
-   `authorize_status = 2`: Từ chối ủy quyền

### 3.3. Commission Calculation

#### 3.3.1. Bonus Types

```php
// CV Type: Thanh toán 100% sau 7 ngày
if ($job->bonus_type === 'cv') {
    $commission = $job->bonus;
    $paymentSchedule = [
        ['percent' => 100, 'delay' => '7 days']
    ];
}

// Interview Type: Thanh toán 100% sau PV thành công
if ($job->bonus_type === 'interview') {
    $commission = $job->bonus;
    $paymentSchedule = [
        ['percent' => 100, 'delay' => '24 hours', 'condition' => 'pass_interview']
    ];
}

// Onboard Type: Thanh toán theo giai đoạn
if ($job->bonus_type === 'onboard') {
    $commission = $job->bonus;
    $paymentSchedule = [
        ['percent' => 15, 'delay' => '30 days', 'condition' => 'trial_work'],
        ['percent' => 10, 'delay' => '45 days', 'condition' => 'trial_work'],
        ['percent' => 75, 'delay' => '67 days', 'condition' => 'success_recruitment']
    ];
}
```

#### 3.3.2. Commission Modifiers

```php
// Percent bonus từ job
if ($submitCv->percent_bonus > 0) {
    $bonusValue = ($job->bonus * $submitCv->percent_bonus) / 100;
    $submitCv->percent_bonus_value = $bonusValue;
}

// Bonus từ NTD (priority cao nhất)
if ($submitCv->bonus_ntd) {
    $finalBonus = $submitCv->bonus_ntd;
} else {
    $finalBonus = $job->bonus + $submitCv->percent_bonus_value;
}
```

### 3.4. Complaint Handling

#### 3.4.1. Complaint Status Flow

```mermaid
stateDiagram-v2
    [*] --> NoComplaint : status_complain = 0
    NoComplaint --> WaitingCTVConfirm : NTD khiếu nại

    WaitingCTVConfirm --> CTVAccepted : CTV chấp nhận
    WaitingCTVConfirm --> WaitingAdminReview : CTV từ chối
    WaitingCTVConfirm --> CTVAccepted : Auto sau 7 ngày

    WaitingAdminReview --> AdminAccepted : Admin chấp nhận
    WaitingAdminReview --> ComplaintFailed : Admin từ chối
    WaitingAdminReview --> AdminAccepted : Auto sau 7 ngày

    CTVAccepted --> [*] : Hoàn tiền NTD
    AdminAccepted --> [*] : Hoàn tiền NTD
    ComplaintFailed --> [*] : Không hoàn tiền
```

#### 3.4.2. Complaint Business Rules

```php
// Điều kiện được khiếu nại
public function canComplain(): bool
{
    // Chỉ khiếu nại được 1 lần
    if ($this->count_complain >= 1) {
        return false;
    }

    // Phải trong trạng thái phù hợp
    $allowedStatuses = [
        config('constant.status_recruitment_revert.BuyCVdatasuccessfull'), // 18
        config('constant.status_recruitment_revert.PassInterview'), // 8
        config('constant.status_recruitment_revert.SuccessRecruitment'), // 16
    ];

    return in_array($this->status, $allowedStatuses);
}

// Auto-approve complaint sau timeout
if ($submitCv->status_complain === 1) { // Waiting CTV
    RecSumExpiredPointSubmit::dispatch($submitCv->id, 1)
        ->delay(now()->addMinutes(7 * 24 * 60)); // 7 days
}

if ($submitCv->status_complain === 2) { // Waiting Admin
    RecSumExpiredPointSubmit::dispatch($submitCv->id, 2)
        ->delay(now()->addMinutes(7 * 24 * 60)); // 7 days
}
```

## 4. Validation Logic

### 4.1. Form Validation Rules

#### 4.1.1. Submit CV Validation

```php
public function rules(): array
{
    return [
        'job_id' => 'required|exists:jobs,id',
        'warehouse_cv_id' => [
            'required',
            'exists:warehouse_cvs,id',
            new CheckDuplicateSubmitByCv($this->warehouse_cv_id, $this->job_id)
        ],
        'expected_date' => 'required|date|after:today',
        'status' => 'required|in:pending-review,draft',
        'authorize' => 'boolean',
    ];
}
```

#### 4.1.2. Status Change Validation

```php
public function validateStatusChange($oldStatus, $newStatus, $userType): bool
{
    $allowedTransitions = config('constant.status_transitions');

    // Kiểm tra transition hợp lệ
    if (!in_array($newStatus, $allowedTransitions[$oldStatus] ?? [])) {
        return false;
    }

    // Kiểm tra quyền của user
    $userPermissions = config('constant.user_permissions')[$userType];
    if (!in_array($newStatus, $userPermissions)) {
        return false;
    }

    return true;
}
```

### 4.2. Business Validation

#### 4.2.1. Interview Booking Validation

```php
public function validateInterviewBooking($submitCv, $bookingData): array
{
    $errors = [];

    // Kiểm tra trạng thái
    if ($submitCv->status !== 1) { // Accepted
        $errors[] = 'Submit CV chưa được chấp nhận';
    }

    // Kiểm tra thời gian
    $bookingTime = Carbon::parse($bookingData['date_book']);
    if ($bookingTime->isPast()) {
        $errors[] = 'Không thể đặt lịch trong quá khứ';
    }

    // Kiểm tra conflict
    $existingBooking = SubmitCvBook::where('submit_cvs_id', $submitCv->id)
        ->where('status', '!=', 2) // Not rejected
        ->exists();

    if ($existingBooking) {
        $errors[] = 'Đã có lịch phỏng vấn cho submit CV này';
    }

    return $errors;
}
```

## 5. Edge Cases và Error Handling

### 5.1. Timeout Scenarios

#### 5.1.1. Candidate Confirmation Timeout

```php
// Sau 48h không xác nhận
if ($submitCv->status === 9 &&
    $submitCv->created_at->addHours(48)->isPast()) {

    // Auto reject
    $submitCv->update(['status' => 12]); // Candidate rejected

    // Notify stakeholders
    $submitCv->rec->notify(new CandidateTimeoutNotification());
    $submitCv->employer->notify(new CandidateTimeoutNotification());
}
```

#### 5.1.2. Interview Confirmation Timeout

```php
// CTV không xác nhận lịch PV trong 48h
if ($book->status === 0 &&
    $book->created_at->addHours(48)->isPast()) {

    // Auto reject interview
    $book->update(['status' => 2]);
    $submitCv->update(['status' => 5]); // Reject Interview schedule
}
```

### 5.2. Concurrent Access Handling

#### 5.2.1. Optimistic Locking

```php
public function updateStatus($submitCvId, $newStatus, $expectedVersion)
{
    $updated = DB::table('submit_cvs')
        ->where('id', $submitCvId)
        ->where('updated_at', $expectedVersion)
        ->update([
            'status' => $newStatus,
            'updated_at' => now()
        ]);

    if ($updated === 0) {
        throw new ConcurrentUpdateException('Record đã được cập nhật bởi user khác');
    }
}
```

### 5.3. Data Consistency Rules

#### 5.3.1. Status Consistency

```php
// Đảm bảo status history được log
DB::transaction(function() use ($submitCv, $newStatus, $user) {
    // Update main record
    $submitCv->update(['status' => $newStatus]);

    // Log history
    SubmitCvHistoryStatus::create([
        'submit_cvs_id' => $submitCv->id,
        'user_id' => $user->id,
        'type' => $user->type,
        'status_recruitment' => $newStatus,
        'created_at' => now()
    ]);
});
```

#### 5.3.2. Payment Consistency

```php
// Đảm bảo payment và status sync
if ($submitCv->status_payment === 2 && // Paid
    !in_array($submitCv->status, [8, 10, 16])) { // Valid paid statuses

    throw new InconsistentStateException('Payment status không khớp với recruitment status');
}
```

### 5.4. Business Rule Exceptions

#### 5.4.1. Emergency Override

```php
// Admin có thể override business rules trong trường hợp khẩn cấp
public function adminOverride($submitCvId, $newStatus, $reason, $adminUser)
{
    if (!$adminUser->hasRole('super_admin')) {
        throw new UnauthorizedException();
    }

    $submitCv = SubmitCv::find($submitCvId);

    // Log override action
    AdminOverrideLog::create([
        'submit_cv_id' => $submitCvId,
        'old_status' => $submitCv->status,
        'new_status' => $newStatus,
        'reason' => $reason,
        'admin_id' => $adminUser->id,
        'created_at' => now()
    ]);

    // Force update
    $submitCv->update(['status' => $newStatus]);
}
```

### 5.5. Integration Error Handling

#### 5.5.1. Email Delivery Failures

```php
try {
    Mail::to($candidate->email)->send(new ConfirmationEmail($submitCv));
} catch (Exception $e) {
    // Log error
    Log::error('Email delivery failed', [
        'submit_cv_id' => $submitCv->id,
        'email' => $candidate->email,
        'error' => $e->getMessage()
    ]);

    // Retry mechanism
    SendEmailJob::dispatch($submitCv, 'confirmation')
        ->delay(now()->addMinutes(5))
        ->onQueue('emails');
}
```

#### 5.5.2. File Upload Failures

```php
try {
    $filePath = FileServiceS3::getInstance()->uploadToS3($file, 'cv');
} catch (S3Exception $e) {
    // Fallback to local storage
    $filePath = Storage::disk('local')->put('cv', $file);

    // Queue for S3 retry
    RetryS3UploadJob::dispatch($filePath)->delay(now()->addMinutes(10));
}
```

---

**Next**: [04_Submit_CV_Jobs_Scheduler.md](./04_Submit_CV_Jobs_Scheduler.md)
