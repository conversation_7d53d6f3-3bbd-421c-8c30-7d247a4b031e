# 01. Submit CV Workflow - Tổng quan

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu chung

### 1.1. <PERSON><PERSON><PERSON> đ<PERSON>ch
Submit CV Workflow là quy trình cốt lõi của hệ thống RecLand, cho phép Cộng tác viên (CTV) giới thiệu ứng viên từ kho CV của mình đến các công việc phù hợp, tạo ra cơ hội kiếm hoa hồng từ việc tuyển dụng thành công.

### 1.2. Phạm vi
- **Bao gồm**: Submit CV workflow (giới thiệu ứng viên)
- **Loại trừ**: Market CV workflow (bán CV trên marketplace)
- **Actors**: CTV, NTD (Nhà tuyển dụng), Admin, Candidate (Ứng viên)

## 2. <PERSON><PERSON>n tr<PERSON><PERSON> tổng quan

### 2.1. High-level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[CTV Dashboard] --> B[Submit CV Form]
        C[Employer Dashboard] --> D[CV Review Interface]
        E[Admin Panel] --> F[Management Interface]
    end
    
    subgraph "Application Layer"
        G[SubmitCvService] --> H[SubmitCvBookService]
        G --> I[SubmitCvOnboardService]
        J[WareHouseSubmitCvService] --> G
    end
    
    subgraph "Background Jobs"
        K[PayInterviewSubmit] --> L[PayOnboardSubmit]
        M[RejectRecruitmentSubmit] --> N[SuccessRecuitmentSubmit]
        O[RecSumExpiredPointSubmit]
    end
    
    subgraph "Data Layer"
        P[(submit_cvs)] --> Q[(submit_cvs_history_status)]
        P --> R[(submit_cvs_books)]
        P --> S[(submit_cvs_onboards)]
    end
    
    subgraph "External Services"
        T[Email Service] --> U[S3 File Storage]
        V[Notification Service]
    end
```

### 2.2. Core Components

#### 2.2.1. Services
- **WareHouseSubmitCvService**: Xử lý submit CV từ kho
- **SubmitCvService**: Quản lý lifecycle của submit CV
- **SubmitCvBookService**: Quản lý lịch phỏng vấn
- **SubmitCvOnboardService**: Quản lý lịch onboard

#### 2.2.2. Controllers
- **WareHouseSubmitCvController**: API endpoints cho submit CV
- **RecController**: Dashboard CTV
- **EmployerController**: Dashboard NTD

#### 2.2.3. Background Jobs
- **PayInterviewSubmit**: Thanh toán sau phỏng vấn (24h delay)
- **PayOnboardSubmit**: Thanh toán theo giai đoạn (30, 45, 67 ngày)
- **RejectRecruitmentSubmit**: Tự động từ chối (48h delay)
- **SuccessRecuitmentSubmit**: Tự động thành công (67 ngày)

## 3. User Journeys

### 3.1. CTV Submit CV Journey

```mermaid
sequenceDiagram
    participant CTV
    participant System
    participant NTD
    participant Candidate
    participant Admin
    
    CTV->>System: Chọn job và CV từ kho
    System->>System: Validate business rules
    System->>Candidate: Gửi email xác nhận
    Candidate->>System: Xác nhận/Từ chối (48h)
    
    alt Candidate xác nhận
        System->>NTD: Thông báo có CV mới
        NTD->>System: Review CV
        
        alt NTD chấp nhận
            NTD->>System: Đặt lịch phỏng vấn
            System->>CTV: Thông báo lịch PV
            CTV->>System: Xác nhận lịch (48h)
            
            alt Phỏng vấn thành công
                System->>System: PayInterviewSubmit (24h delay)
                NTD->>System: Đặt lịch onboard
                System->>System: ChangeToTrailWorkSubmit (7 days)
                System->>System: PayOnboardSubmit (30, 45, 67 days)
            end
        else NTD từ chối
            System->>CTV: Thông báo từ chối
        end
    else Candidate từ chối
        System->>System: RejectRecruitmentSubmit
    end
```

### 3.2. Payment Flow

```mermaid
graph LR
    A[Submit CV] --> B{Bonus Type}
    
    B -->|CV| C[Thanh toán sau 7 ngày]
    B -->|Interview| D[Thanh toán sau PV thành công]
    B -->|Onboard| E[Thanh toán theo giai đoạn]
    
    D --> F[PayInterviewSubmit - 24h delay]
    E --> G[PayOnboardSubmit - 30 days: 15%]
    G --> H[PayOnboardSubmit - 45 days: 10%]
    H --> I[PayOnboardSubmit - 67 days: 75%]
```

## 4. Key Stakeholders

### 4.1. Cộng tác viên (CTV)
- **Vai trò**: Giới thiệu ứng viên từ kho CV
- **Quyền lợi**: Nhận hoa hồng khi tuyển dụng thành công
- **Trách nhiệm**: Đảm bảo chất lượng CV và ứng viên

### 4.2. Nhà tuyển dụng (NTD)
- **Vai trò**: Đánh giá và tuyển dụng ứng viên
- **Quyền lợi**: Nhận được ứng viên chất lượng
- **Trách nhiệm**: Thanh toán phí dịch vụ, feedback kịp thời

### 4.3. Admin
- **Vai trò**: Quản lý và giám sát hệ thống
- **Quyền lợi**: Kiểm soát chất lượng dịch vụ
- **Trách nhiệm**: Xử lý khiếu nại, ủy quyền

### 4.4. Candidate (Ứng viên)
- **Vai trò**: Đối tượng được giới thiệu
- **Quyền lợi**: Nhận được cơ hội việc làm
- **Trách nhiệm**: Xác nhận tham gia quy trình

## 5. Business Model

### 5.1. Revenue Streams
- **CV Fee**: Phí mở CV (bonus_type = 'cv')
- **Interview Fee**: Phí phỏng vấn (bonus_type = 'interview')  
- **Onboard Fee**: Phí onboard (bonus_type = 'onboard')

### 5.2. Commission Structure
- **CV**: 100% sau 7 ngày không khiếu nại
- **Interview**: 100% sau phỏng vấn thành công (24h delay)
- **Onboard**: 15% (30 ngày) + 10% (45 ngày) + 75% (67 ngày)

### 5.3. Authorization Model
- **authorize = 0**: CTV tự quản lý
- **authorize = 1**: Admin quản lý thay CTV (ủy quyền)

## 6. Key Features

### 6.1. Submit CV từ kho
- Chọn CV từ warehouse
- Validate business rules
- Gửi email xác nhận candidate

### 6.2. Status Management
- 13 trạng thái chính từ Pending đến Success
- Automatic transitions với background jobs
- Manual transitions bởi stakeholders

### 6.3. Interview Scheduling
- NTD đặt lịch phỏng vấn
- CTV xác nhận trong 48h
- Auto-reject nếu không xác nhận

### 6.4. Onboard Process
- Lên lịch onboard sau PV thành công
- Trial work period (60 ngày)
- Automatic success sau 67 ngày

### 6.5. Complaint System
- NTD có thể khiếu nại
- CTV/Admin xử lý trong 7 ngày
- Auto-approve nếu không phản hồi

### 6.6. Payment Processing
- Automatic payment theo schedule
- Commission calculation
- Refund handling

## 7. Technical Constraints

### 7.1. Performance Requirements
- Submit CV response time < 2s
- Background job processing < 5 minutes
- File upload < 10MB

### 7.2. Security Requirements
- Authentication required cho tất cả operations
- Authorization based on user roles
- File access control với S3 signed URLs

### 7.3. Scalability Requirements
- Support 1000+ concurrent users
- Queue-based background processing
- Database indexing cho performance

## 8. Integration Points

### 8.1. External Services
- **AWS S3**: File storage cho CV files
- **Email Service**: Notification delivery
- **Payment Gateway**: Commission processing

### 8.2. Internal Services
- **User Management**: Authentication/Authorization
- **Job Management**: Job listings và requirements
- **Warehouse CV**: CV storage và management

---

**Next**: [02_Submit_CV_Database_Schema.md](./02_Submit_CV_Database_Schema.md)
