# Task Completion Workflow - RecLand

## Quy trình hoàn thành task

### 1. Code Quality Checks
```powershell
# Check PHP syntax
php -l file.php

# Run PHP CodeSniffer (nếu có)
./vendor/bin/phpcs app/

# Fix coding standards
./vendor/bin/phpcbf app/
```

### 2. Testing (Không bắt buộc cho PHP projects)
```powershell
# Run unit tests
./vendor/bin/phpunit

# Run specific test file
./vendor/bin/phpunit tests/Feature/FeatureTest.php

# Generate test coverage
./vendor/bin/phpunit --coverage-html coverage
```

### 3. Database Operations
```powershell
# Run new migrations
php artisan migrate

# Check migration status
php artisan migrate:status

# Rollback if needed
php artisan migrate:rollback
```

### 4. Asset Compilation
```powershell
# Compile assets for development
npm run dev

# Compile for production
npm run prod

# Watch for changes during development
npm run watch
```

### 5. Cache Management
```powershell
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 6. Code Review Checklist
- [ ] Code follows PSR-12 standards
- [ ] Proper error handling implemented
- [ ] Input validation added
- [ ] Database queries optimized
- [ ] Security considerations addressed
- [ ] Documentation updated
- [ ] No hardcoded values
- [ ] Proper logging implemented

### 7. Deployment Preparation
```powershell
# Generate optimized autoloader
composer dump-autoload --optimize

# Clear and cache configurations
php artisan config:cache
php artisan route:cache

# Compile production assets
npm run prod
```

### 8. Documentation Updates
- Update README if needed
- Document new API endpoints
- Update code comments
- Create/update workflow files in memory_bank/

### 9. Final Verification
- Test functionality manually
- Check error logs
- Verify database changes
- Test on different browsers (if frontend changes)

## Notes
- Với dự án PHP/Laravel, việc chạy automated tests không bắt buộc
- Tập trung vào manual testing và code review
- Đảm bảo code hoạt động đúng trong môi trường development
- Kiểm tra logs để phát hiện lỗi tiềm ẩn