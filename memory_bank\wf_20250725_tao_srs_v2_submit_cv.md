# Workflow: Tạo SRS v2.0 Submit CV - 25/07/2025

## Tổng quan công việc

**<PERSON><PERSON><PERSON> tiêu**: Phân tích toàn bộ codebase RecLand và viết lại tài liệu Software Requirements Specification (SRS) phiên bản 2.0 với độ chi tiết cao nhất cho Submit CV workflow.

**Phạm vi**: Submit CV workflow (tạm thời bỏ qua Market CV workflow)

## Các bước đã thực hiện

### 1. Thu thập và phân tích thông tin (Hoàn thành)

#### 1.1. Phân tích codebase

-   **Services**: SubmitCvService, SubmitCvBookService, SubmitCvOnboardService, WareHouseSubmitCvService
-   **Controllers**: WareHouseSubmitCvController, <PERSON><PERSON><PERSON><PERSON>roller, EmployerController
-   **Models**: SubmitCv với các relationships
-   **Jobs**: PayInterviewSubmit, PayOnboardSubmit, RejectRecruitmentSubmit, SuccessRecuitmentSubmit, RecSumExpiredPointSubmit
-   **Notifications**: Hệ thống email và in-app notifications

#### 1.2. Database schema analysis

-   Bảng chính: submit_cvs với 30+ columns
-   Bảng liên quan: submit_cvs_history_status, submit_cvs_books, submit_cvs_onboards, submit_cvs_history_payment
-   Migration files từ 2022-2025
-   Indexes và foreign key constraints

#### 1.3. Business logic analysis

-   Status definitions: 13 trạng thái chính (0-12, 14, 16, 17)
-   Status transitions và business rules
-   Authorization logic (authorize = 0/1)
-   Commission calculation theo bonus_type
-   Complaint handling workflow

#### 1.4. Background jobs analysis

-   Payment jobs với delay times cụ thể
-   Timeout jobs cho auto-reject scenarios
-   Status transition jobs
-   Notification jobs

#### 1.5. API endpoints analysis

-   Web routes và API routes
-   Request/response formats
-   Authentication requirements
-   Error handling

### 2. Tạo cấu trúc tài liệu (Hoàn thành)

#### 2.1. Thư mục và file index

-   Tạo thư mục `document/SRS_v2/`
-   File `00_SRS_v2_Index.md` với mục lục tổng hợp

#### 2.2. Cấu trúc 8 files chính

1. `01_Submit_CV_Overview.md` - Tổng quan workflow
2. `02_Submit_CV_Database_Schema.md` - Cấu trúc database
3. `03_Submit_CV_Business_Logic.md` - Logic nghiệp vụ
4. `04_Submit_CV_Jobs_Scheduler.md` - Background jobs
5. `05_Submit_CV_API_Endpoints.md` - API documentation
6. `06_Submit_CV_Frontend_Components.md` - Frontend components
7. `07_Submit_CV_Notifications.md` - Notification system
8. `08_Submit_CV_State_Machine.md` - State machine diagrams

### 3. Viết tài liệu chi tiết (Đã hoàn thành 6/8 files)

#### 3.1. ✅ 01_Submit_CV_Overview.md

-   High-level architecture với Mermaid diagrams
-   User journeys cho CTV, NTD, Admin, Candidate
-   Payment flow diagrams
-   Key stakeholders và business model
-   Technical constraints

#### 3.2. ✅ 02_Submit_CV_Database_Schema.md

-   DDL scripts đầy đủ cho tất cả tables
-   Column descriptions chi tiết
-   Status mapping với ID và text
-   Relationships diagram
-   Sample data examples
-   Foreign key constraints
-   Performance indexes

#### 3.3. ✅ 03_Submit_CV_Business_Logic.md (TÁI CẤU TRÚC HOÀN THÀNH)

-   **Tái cấu trúc theo 3 bonus_type**: CV, Interview, Onboard
-   **CV Type**: Submit → Buy CV → Payment (7 ngày)
-   **Interview Type**: Submit → Interview → Payment (24h nếu pass)
-   **Onboard Type**: Submit → Interview → Onboard → Trial → Success (15%+10%+75%)
-   State transition diagrams riêng cho từng type
-   Business rules và validation cụ thể
-   Commission calculation formulas chi tiết
-   Timeout scenarios và auto-transitions
-   Common business logic (complaint, authorization)

#### 3.4. ✅ 04_Submit_CV_Jobs_Scheduler.md

-   Tất cả background jobs với code examples
-   Delay times và trigger conditions
-   Payment schedule chi tiết
-   Job monitoring và error handling
-   Queue prioritization
-   Job dependencies và ordering

#### 3.5. ✅ 05_Submit_CV_API_Endpoints.md

-   Tất cả endpoints với request/response examples
-   Authentication requirements
-   Validation rules
-   Error handling và status codes
-   Rate limiting
-   Webhook endpoints

### 4. Còn lại cần hoàn thành (2/8 files)

#### 4.1. 🔄 07_Submit_CV_Notifications.md

-   Email templates chi tiết
-   In-app notifications
-   Notification triggers
-   Template variables
-   Delivery mechanisms

#### 4.2. 🔄 08_Submit_CV_State_Machine.md

-   State transition diagrams
-   Workflow decision points
-   Mermaid diagrams cho lifecycle
-   Edge cases handling

### 5. Cập nhật mới nhất (25/07/2025)

#### 5.1. ✅ Tái cấu trúc Business Logic hoàn thành

-   **Phân chia theo 3 bonus_type**: CV, Interview, Onboard
-   **CV Type workflow**:
    -   9 (Pending confirm) → 21 (Waiting Payment) → 18 (Buy CV successful)
    -   Payment: 100% sau 7 ngày không khiếu nại
    -   Jobs: RecSumPointSubmit
-   **Interview Type workflow**:
    -   9 → 21 → 3 (Waiting setup interview) → 4 (Waiting confirm calendar) → 7 (Waiting Interview) → 8 (Pass) / 10 (Fail)
    -   Payment: 100% sau 24h nếu pass interview
    -   Jobs: PayInterviewSubmit, RejectBookExpireSubmit
-   **Onboard Type workflow**:
    -   9 → 21 → 3 → 4 → 7 → 8 → 11 (Offering) → 13 (Onboarded) → 14 (Trial work) → 16 (Success)
    -   Payment: 15% (30 ngày) + 10% (45 ngày) + 75% (67 ngày)
    -   Jobs: PayOnboardSubmit, ChangeToTrailWorkSubmit, SuccessRecuitmentSubmit

#### 5.2. ✅ State diagrams riêng cho từng type

-   Mermaid diagrams chi tiết cho từng workflow
-   Business rules và validation cụ thể
-   Timeout scenarios và auto-transitions
-   Error handling và edge cases

## Thông tin kỹ thuật đã phân tích

### Status Codes đã mapping

```php
'submit_cvs_status' => [
    9 => 'Pending confirm',      // Chờ ứng viên xác nhận
    0 => 'Pending review',       // Chờ NTD review
    1 => 'Accepted',            // NTD chấp nhận
    2 => 'Rejected',            // NTD từ chối
    3 => 'Pass-interview',      // Phỏng vấn thành công
    4 => 'Fail-interview',      // Phỏng vấn thất bại
    8 => 'Offering',            // Đang offer
    5 => 'Onboarded',           // Đã onboard
    6 => 'Cancel',              // Hủy
    7 => 'Draft',               // Nháp
    10 => 'Admin review',       // Admin review
    11 => 'Admin rejected',     // Admin từ chối
    12 => 'Candidate rejected', // Ứng viên từ chối
    14 => 'Trial work',         // Thử việc
    16 => 'Success Recruitment', // Thành công
    17 => 'Fail trial work',    // Thử việc thất bại
]
```

### Jobs đã phân tích

-   **PayInterviewSubmit**: 24h delay sau pass interview
-   **PayOnboardSubmit**: 30, 45, 67 ngày với % khác nhau
-   **RejectRecruitmentSubmit**: 48h timeout
-   **SuccessRecuitmentSubmit**: 67 ngày auto success
-   **RecSumExpiredPointSubmit**: 7 ngày complaint timeout

### Database Tables đã document

-   submit_cvs (30+ columns)
-   submit_cvs_history_status
-   submit_cvs_books
-   submit_cvs_onboards
-   submit_cvs_history_payment

## Kết quả đạt được

### Chất lượng tài liệu

✅ **Độ chi tiết cao**: Đủ để developer khác implement từ đầu  
✅ **Code examples**: Có đầy đủ PHP code, SQL scripts  
✅ **Visual diagrams**: Mermaid diagrams cho workflows  
✅ **Sample data**: Examples cho tất cả entities  
✅ **Error scenarios**: Edge cases và error handling

### Cấu trúc hoàn chỉnh

-   8 files tài liệu với mục lục tổng hợp
-   Mỗi file có structure rõ ràng và cross-references
-   Markdown format với syntax highlighting
-   Mermaid diagrams cho complex workflows

## Tiếp theo cần làm

1. **Hoàn thành 3 files còn lại**:

    - 06_Submit_CV_Frontend_Components.md
    - 07_Submit_CV_Notifications.md
    - 08_Submit_CV_State_Machine.md

2. **Review và optimize**:

    - Kiểm tra tính nhất quán giữa các files
    - Bổ sung cross-references
    - Validate với business requirements

3. **Testing documentation**:
    - Verify code examples
    - Test API endpoints
    - Validate database scripts

## Lessons Learned

1. **Codebase analysis**: Cần phân tích từ nhiều góc độ (Models, Services, Jobs, Controllers)
2. **Documentation structure**: Chia nhỏ thành nhiều files giúp dễ maintain
3. **Visual diagrams**: Mermaid diagrams rất hữu ích cho complex workflows
4. **Code examples**: Cần có đầy đủ examples để developer hiểu rõ implementation

---

**Status**: 5/8 files hoàn thành (62.5%)  
**Next**: Tiếp tục với Frontend Components documentation
