# 05. Submit CV API Endpoints

## 1. Tổng quan API

### 1.1. Base URL

-   **Production**: `https://recland.com`
-   **Staging**: `https://staging.recland.com`
-   **Local**: `http://localhost:8000`

### 1.2. Authentication

-   **Web Routes**: Session-based authentication
-   **API Routes**: Sanctum token authentication
-   **Admin Routes**: Admin middleware + session

### 1.3. Response Format

```json
{
    "code": 200,
    "status": "success|error",
    "message": "Response message",
    "data": {},
    "errors": {}
}
```

## 2. Submit CV Core Endpoints

### 2.1. Submit CV từ kho

#### 2.1.1. POST /save-cv-submit

**Purpose**: Submit CV từ warehouse cho job

**Route**: `Route::post('save-cv-submit', [WareHouseSubmitCvController::class, 'index'])->name('user-save-cv-submit');`

**Authentication**: Required (CTV)

**Request**:

```json
{
    "job_id": 123,
    "warehouse_cv_id": 456,
    "expected_date": "2025-08-15",
    "status": "pending-review",
    "authorize": 0,
    "tab": "chontukho"
}
```

**Validation Rules**:

```php
[
    'job_id' => 'required|exists:jobs,id',
    'warehouse_cv_id' => [
        'required',
        'exists:warehouse_cvs,id',
        new CheckDuplicateSubmitByCv($this->warehouse_cv_id, $this->job_id)
    ],
    'expected_date' => 'required|date|after:today',
    'status' => 'required|in:pending-review,draft',
    'authorize' => 'boolean',
]
```

**Response Success**:

```json
{
    "status": "success",
    "candidate_name": "Nguyễn Văn A",
    "redirect": "/rec/submitcv"
}
```

**Response Error**:

```json
{
    "status": "error",
    "message": "CV đã được submit cho job này",
    "errors": {
        "warehouse_cv_id": ["Duplicate submit detected"]
    }
}
```

#### 2.1.2. POST /user-candidate-introduction

**Purpose**: Giới thiệu ứng viên mới (không từ kho)

**Request**:

```json
{
    "job_id": 123,
    "candidate_name": "Nguyễn Văn B",
    "candidate_email": "<EMAIL>",
    "candidate_phone": "0901234567",
    "cv_file": "base64_encoded_file",
    "introduction_letter": "Giới thiệu ứng viên...",
    "authorize": 0
}
```

### 2.2. Get CV Data

#### 2.2.1. GET /get-cv-by-user

**Purpose**: Lấy danh sách CV của CTV

**Authentication**: Required (CTV)

**Query Parameters**:

```
?search=keyword&page=1&limit=10
```

**Response**:

```json
{
    "data": [
        {
            "id": 456,
            "candidate_name": "Nguyễn Văn A",
            "candidate_email": "<EMAIL>",
            "position": "Senior Developer",
            "experience_years": 5,
            "cv_file_url": "https://s3.amazonaws.com/...",
            "created_at": "2025-07-20T10:00:00Z"
        }
    ],
    "pagination": {
        "current_page": 1,
        "total_pages": 5,
        "total_items": 50
    }
}
```

#### 2.2.2. POST /get-cv-by-id

**Purpose**: Lấy chi tiết CV theo ID

**Request**:

```json
{
    "cv_id": 456
}
```

**Response**:

```json
{
    "data": {
        "id": 456,
        "candidate_name": "Nguyễn Văn A",
        "candidate_email": "<EMAIL>",
        "candidate_phone": "0901234567",
        "position": "Senior Developer",
        "experience_years": 5,
        "current_salary": 20000000,
        "expected_salary": 25000000,
        "cv_file_url": "https://s3.amazonaws.com/...",
        "skills": ["PHP", "Laravel", "Vue.js"],
        "education": "Đại học Bách Khoa",
        "created_at": "2025-07-20T10:00:00Z"
    }
}
```

## 3. File Upload Endpoints

### 3.1. POST /upload-public-cv

**Purpose**: Upload CV công khai (đã che thông tin)

**Request**: Multipart form data

```
file: CV file (PDF/DOC/DOCX)
cv_id: 456 (optional)
```

**Response**:

```json
{
    "code": 200,
    "status": "success",
    "cv_public": "path/to/public/cv.pdf",
    "cv_public_link": "https://s3.amazonaws.com/public/cv.pdf",
    "hide_cv_response": {
        "statusCode": 200,
        "data": {
            "file_url": "https://s3.amazonaws.com/hidden/cv.pdf"
        }
    }
}
```

### 3.2. POST /upload-private-cv

**Purpose**: Upload CV riêng tư (đầy đủ thông tin)

**Request**: Multipart form data

```
private_cv: CV file
cv_id: 456 (optional)
```

**Response**:

```json
{
    "code": 200,
    "status": "success",
    "cv_private": "path/to/private/cv.pdf",
    "cv_private_link": "https://s3.amazonaws.com/private/cv.pdf"
}
```

### 3.3. POST /upload-private-cv-for-submit-cv

**Purpose**: Upload CV riêng tư cho submit CV cụ thể

**Request**: Multipart form data

```
private_cv: CV file
submit_id: 789
```

## 4. Status Management Endpoints

### 4.1. POST /employer/submitcv/change-status

**Purpose**: NTD thay đổi trạng thái submit CV

**Authentication**: Required (Employer)

**Request**:

```json
{
    "submit_cv_id": 789,
    "status": 1,
    "assessment": "Ứng viên phù hợp với yêu cầu",
    "reason": "Good technical skills"
}
```

**Validation**:

```php
[
    'submit_cv_id' => 'required|exists:submit_cvs,id',
    'status' => 'required|integer|in:1,2,3,4,8,12',
    'assessment' => 'required_if:status,2,4|string|max:1000',
    'reason' => 'nullable|string|max:500'
]
```

**Response**:

```json
{
    "status": "success",
    "message": "Cập nhật trạng thái thành công",
    "data": {
        "submit_cv_id": 789,
        "new_status": 1,
        "status_text": "Accepted"
    }
}
```

### 4.2. GET /employer/submitcv/detail/{id}

**Purpose**: Xem chi tiết submit CV

**Authentication**: Required (Employer)

**Response**:

```json
{
    "data": {
        "id": 789,
        "code": "SUBMIT_CV_20250725_001",
        "status": 1,
        "status_text": "Accepted",
        "candidate": {
            "name": "Nguyễn Văn A",
            "email": "<EMAIL>",
            "phone": "0901234567",
            "position": "Senior Developer",
            "experience_years": 5
        },
        "job": {
            "id": 123,
            "title": "Senior PHP Developer",
            "company": "ABC Company"
        },
        "recruiter": {
            "id": 456,
            "name": "CTV Nguyễn Thị B",
            "company": "Recruitment Agency"
        },
        "cv_files": {
            "public_cv": "https://s3.amazonaws.com/public/cv.pdf",
            "private_cv": "https://s3.amazonaws.com/private/cv.pdf"
        },
        "timeline": [
            {
                "status": 9,
                "status_text": "Pending confirm",
                "changed_by": "System",
                "changed_at": "2025-07-25T10:00:00Z",
                "comment": "CV submitted by recruiter"
            },
            {
                "status": 0,
                "status_text": "Pending review",
                "changed_by": "Candidate",
                "changed_at": "2025-07-25T12:00:00Z",
                "comment": "Candidate confirmed participation"
            }
        ],
        "created_at": "2025-07-25T10:00:00Z",
        "updated_at": "2025-07-25T12:00:00Z"
    }
}
```

## 5. Interview Booking Endpoints

### 5.1. POST /employer/schedule-interview-submit

**Purpose**: NTD đặt lịch phỏng vấn

**Authentication**: Required (Employer)

**Request**:

```json
{
    "submit_cv_id": 789,
    "date_book": "2025-08-01 14:00:00",
    "address": "Tầng 5, Tòa nhà ABC, Quận 1, TP.HCM",
    "phone": "0901234567",
    "name": "Nguyễn Văn A",
    "book_time_minute": 60,
    "note": "Mang theo laptop và portfolio"
}
```

**Validation**:

```php
[
    'submit_cv_id' => 'required|exists:submit_cvs,id',
    'date_book' => 'required|date|after:now',
    'address' => 'required|string|max:255',
    'phone' => 'required|string|max:20',
    'name' => 'required|string|max:100',
    'book_time_minute' => 'required|integer|min:30|max:180',
    'note' => 'nullable|string|max:500'
]
```

**Response**:

```json
{
    "status": "success",
    "message": "Đặt lịch phỏng vấn thành công",
    "data": {
        "book_id": 123,
        "submit_cv_id": 789,
        "date_book": "2025-08-01T14:00:00Z",
        "status": 0,
        "status_text": "Waiting for CTV confirmation"
    }
}
```

### 5.2. POST /rec/confirm-interview

**Purpose**: CTV xác nhận lịch phỏng vấn

**Authentication**: Required (CTV)

**Request**:

```json
{
    "book_id": 123,
    "action": "confirm",
    "note": "CTV xác nhận lịch phỏng vấn"
}
```

**Response**:

```json
{
    "status": "success",
    "message": "Xác nhận lịch phỏng vấn thành công",
    "data": {
        "book_id": 123,
        "status": 1,
        "status_text": "Confirmed"
    }
}
```

## 6. Complaint Endpoints

### 6.1. POST /employer/submit-complaint

**Purpose**: NTD gửi khiếu nại

**Authentication**: Required (Employer)

**Request**:

```json
{
    "submit_cv_id": 789,
    "txt_complain": "Ứng viên không đúng như mô tả",
    "img_complain": "base64_encoded_image"
}
```

**Response**:

```json
{
    "status": "success",
    "message": "Gửi khiếu nại thành công",
    "data": {
        "submit_cv_id": 789,
        "status_complain": 1,
        "status_complain_text": "Chờ CTV xác nhận"
    }
}
```

### 6.2. POST /rec/respond-complaint

**Purpose**: CTV phản hồi khiếu nại

**Authentication**: Required (CTV)

**Request**:

```json
{
    "submit_cv_id": 789,
    "action": "accept",
    "response_note": "CTV chấp nhận khiếu nại và hoàn tiền"
}
```

**Values for action**: `accept`, `reject`

## 7. Admin Endpoints

### 7.1. GET /admin/submit-cv

**Purpose**: Admin xem danh sách submit CV

**Authentication**: Required (Admin)

**Query Parameters**:

```
?search=keyword&status=1&user_id=123&company_id=456&page=1&limit=20
```

**Response**:

```json
{
    "data": [
        {
            "id": 789,
            "code": "SUBMIT_CV_20250725_001",
            "candidate_name": "Nguyễn Văn A",
            "job_title": "Senior PHP Developer",
            "company_name": "ABC Company",
            "recruiter_name": "CTV Nguyễn Thị B",
            "status": 1,
            "status_text": "Accepted",
            "bonus_amount": 5000000,
            "created_at": "2025-07-25T10:00:00Z"
        }
    ],
    "pagination": {
        "current_page": 1,
        "total_pages": 10,
        "total_items": 200
    }
}
```

### 7.2. POST /admin/submit-cv/{id}/update

**Purpose**: Admin cập nhật submit CV

**Authentication**: Required (Admin)

**Request**:

```json
{
    "status": 1,
    "status_payment": 2,
    "assessment": "Admin approved",
    "authorize_status": 1,
    "admin_note": "Processed by admin"
}
```

### 7.3. POST /admin/submit-cv/preview/update-complain

**Purpose**: Admin xử lý khiếu nại

**Authentication**: Required (Admin)

**Request**:

```json
{
    "submit_cv_id": 789,
    "action": "approve",
    "admin_note": "Khiếu nại hợp lệ, hoàn tiền cho NTD"
}
```

## 8. Error Handling

### 8.1. HTTP Status Codes

-   **200**: Success
-   **400**: Bad Request (validation errors)
-   **401**: Unauthorized
-   **403**: Forbidden
-   **404**: Not Found
-   **422**: Unprocessable Entity (business logic errors)
-   **500**: Internal Server Error

### 8.2. Error Response Format

```json
{
    "code": 422,
    "status": "error",
    "message": "Validation failed",
    "errors": {
        "warehouse_cv_id": ["CV đã được submit cho job này"],
        "expected_date": ["Ngày dự kiến phải sau ngày hiện tại"]
    }
}
```

### 8.3. Business Logic Errors

```json
{
    "code": 422,
    "status": "error",
    "message": "Không thể thực hiện thao tác",
    "error_code": "INVALID_STATUS_TRANSITION",
    "details": {
        "current_status": 2,
        "requested_status": 3,
        "reason": "Không thể chuyển từ Rejected sang Pass Interview"
    }
}
```

## 9. Rate Limiting

### 9.1. Rate Limits

-   **Submit CV**: 10 requests/minute per user
-   **File Upload**: 5 requests/minute per user
-   **Status Update**: 20 requests/minute per user
-   **General API**: 60 requests/minute per user

### 9.2. Rate Limit Headers

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1627834800
```

## 10. API Versioning

### 10.1. Current Version

-   **Version**: v1
-   **Base Path**: `/api/v1/`
-   **Backward Compatibility**: Maintained for 12 months

### 10.2. Version Headers

```
Accept: application/json
API-Version: v1
```

## 11. Webhook Endpoints

### 11.1. Status Change Webhook

**Purpose**: Notify external systems về status changes

**Endpoint**: Configured per client

**Payload**:

```json
{
    "event": "submit_cv.status_changed",
    "data": {
        "submit_cv_id": 789,
        "old_status": 0,
        "new_status": 1,
        "changed_by": "employer",
        "changed_at": "2025-07-25T14:00:00Z"
    },
    "timestamp": "2025-07-25T14:00:00Z",
    "signature": "sha256_hash"
}
```

### 11.2. Payment Webhook

**Purpose**: Notify về payment events

**Payload**:

```json
{
    "event": "submit_cv.payment_processed",
    "data": {
        "submit_cv_id": 789,
        "amount": 5000000,
        "currency": "VND",
        "payment_type": "commission",
        "processed_at": "2025-07-25T14:00:00Z"
    }
}
```

---

**Next**: [06_Submit_CV_Frontend_Components.md](./06_Submit_CV_Frontend_Components.md)
