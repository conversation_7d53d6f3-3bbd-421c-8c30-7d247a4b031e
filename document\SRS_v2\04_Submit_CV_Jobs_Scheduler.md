# 04. Submit CV Jobs Scheduler

## 1. <PERSON><PERSON><PERSON> quan Background Jobs

### 1.1. Job Categories

-   **Payment Jobs**: Xử lý thanh toán commission
-   **Timeout Jobs**: Xử lý các timeout scenarios
-   **Notification Jobs**: Gửi email và notifications
-   **Status Jobs**: Tự động chuyển đổi trạng thái

### 1.2. Job Execution Flow

```mermaid
graph TB
    A[Submit CV Created] --> B{Bonus Type}

    B -->|CV| C[RecSumPointSubmit - 7 days]
    B -->|Interview| D[RejectRecruitmentSubmit - 48h]
    B -->|Onboard| E[RejectRecruitmentSubmit - 48h]

    F[Pass Interview] --> G[PayInterviewSubmit - 24h]

    H[Onboard Scheduled] --> I[ChangeToTrailWorkSubmit - 7 days]
    I --> J[PayOnboardSubmit - 30 days: 15%]
    J --> K[PayOnboardSubmit - 45 days: 10%]
    K --> L[PayOnboardSubmit - 67 days: 75%]
    I --> M[SuccessRecuitmentSubmit - 67 days]

    N[Complaint Created] --> O[RecSumExpiredPointSubmit - 7 days]
```

## 2. Payment Jobs

### 2.1. PayInterviewSubmit

#### 2.1.1. Job Definition

```php
class PayInterviewSubmit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $submitCvId;

    public function __construct($submitCvId)
    {
        $this->submitCvId = $submitCvId;
    }

    public function handle()
    {
        $submitCv = SubmitCv::find($this->submitCvId);

        // Kiểm tra điều kiện thanh toán
        if (($submitCv->status == 8 || $submitCv->status == 10) &&
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5)) {

            $submitCvService = resolve(SubmitCvService::class);
            $submitCvService->payCtv($submitCv);
        }
    }
}
```

#### 2.1.2. Trigger Conditions

-   **Dispatch**: Khi status chuyển thành Pass Interview (8)
-   **Delay**: 24 giờ sau khi pass interview
-   **Conditions**:
    -   Status = 8 (Pass Interview) hoặc 10 (Fail Interview)
    -   Không có khiếu nại hoặc khiếu nại thất bại

#### 2.1.3. Business Logic

```php
public function payCtv($submitCv)
{
    // Tính toán commission
    $commission = $this->calculateCommission($submitCv);

    // Cập nhật payment status
    $submitCv->update([
        'status_payment' => 2, // Paid
        'payment_actual' => $commission,
        'date_change_status_payment' => now()
    ]);

    // Cộng tiền vào ví CTV
    $this->addBalanceToUser($submitCv->user_id, $commission);

    // Log payment history
    $this->logPaymentHistory($submitCv, $commission);

    // Send notification
    $submitCv->rec->notify(new PaymentInterviewToRecSubmit($submitCv, $commission));
}
```

### 2.2. PayOnboardSubmit

#### 2.2.1. Job Definition

```php
class PayOnboardSubmit implements ShouldQueue
{
    public $submitCvId;
    public $percent;

    public function __construct($submitCvId, $percent)
    {
        $this->submitCvId = $submitCvId;
        $this->percent = $percent;
    }

    public function handle()
    {
        $submitCv = SubmitCv::find($this->submitCvId);

        // Kiểm tra điều kiện
        if (($submitCv->status == 14 || $submitCv->status == 16) &&
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5)) {

            $submitCvService = resolve(SubmitCvService::class);
            $submitCvService->payOnboardCtv($submitCv, $this->percent);
        }
    }
}
```

#### 2.2.2. Payment Schedule

```php
// Dispatch từ ChangeToTrailWorkSubmit
$carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book);

// Thanh toán 15% sau 30 ngày
PayOnboardSubmit::dispatch($submitCv->id, 15)
    ->delay($carbonDate->addMinutes(30 * 24 * 60));

// Thanh toán 10% sau 45 ngày
PayOnboardSubmit::dispatch($submitCv->id, 10)
    ->delay($carbonDate->addMinutes(45 * 24 * 60));

// Thanh toán 75% sau 67 ngày
PayOnboardSubmit::dispatch($submitCv->id, 75)
    ->delay($carbonDate->addMinutes(67 * 24 * 60));
```

### 2.3. RecSumPointSubmit

#### 2.3.1. Job Definition

```php
class RecSumPointSubmit implements ShouldQueue
{
    public $submitCvId;

    public function handle()
    {
        $submitCv = SubmitCv::find($this->submitCvId);

        // Chỉ thanh toán cho CV type
        if ($submitCv->bonus_type !== 'cv') {
            return;
        }

        // Kiểm tra điều kiện
        if ($submitCv->status == 18 && // Buy CV data successful
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5)) {

            $this->payCommission($submitCv);
        }
    }
}
```

#### 2.3.2. Trigger Conditions

-   **Dispatch**: Khi NTD mua CV thành công
-   **Delay**: 7 ngày sau khi mua CV
-   **Purpose**: Thanh toán cho CTV sau khi hết thời gian khiếu nại

## 3. Timeout Jobs

### 3.1. RejectRecruitmentSubmit

#### 3.1.1. Job Definition

```php
class RejectRecruitmentSubmit implements ShouldQueue
{
    public $submitCvId;

    public function handle()
    {
        $submitCv = SubmitCv::find($this->submitCvId);

        // Kiểm tra vẫn ở trạng thái pending confirm
        if ($submitCv->status == 9) { // Pending confirm
            // Auto reject do candidate không xác nhận
            $submitCv->update([
                'status' => 12, // Candidate rejected
                'date_change_status' => now()
            ]);

            // Log history
            $this->logStatusHistory($submitCv, 'auto_reject_timeout');

            // Send notifications
            $this->sendTimeoutNotifications($submitCv);
        }
    }
}
```

#### 3.1.2. Trigger Conditions

-   **Dispatch**: Khi tạo submit CV mới
-   **Delay**: 48 giờ
-   **Purpose**: Tự động từ chối nếu candidate không xác nhận

### 3.2. RejectBookExpireSubmit

#### 3.2.1. Job Definition

```php
class RejectBookExpireSubmit implements ShouldQueue
{
    public $book;
    public $user;

    public function handle()
    {
        // Kiểm tra CTV chưa xác nhận lịch PV
        if ($this->book->status == 0) { // Just scheduled
            // Auto reject
            $this->book->update(['status' => 2]); // Rejected

            // Update submit CV status
            $submitCv = $this->book->submitCv;
            $submitCv->update(['status' => 5]); // Reject Interview schedule

            // Send notifications
            $this->sendRejectionNotifications($submitCv, $this->book);
        }
    }
}
```

#### 3.2.2. Trigger Conditions

-   **Dispatch**: Khi NTD đặt lịch phỏng vấn
-   **Delay**: 48 giờ
-   **Purpose**: Tự động từ chối lịch PV nếu CTV không xác nhận

### 3.3. RecSumExpiredPointSubmit

#### 3.3.1. Job Definition

```php
class RecSumExpiredPointSubmit implements ShouldQueue
{
    public $submitCvId;
    public $statusComplain;

    public function handle()
    {
        $submitCv = SubmitCv::find($this->submitCvId);

        // Kiểm tra vẫn ở trạng thái chờ xử lý khiếu nại
        if ($submitCv->status_complain == $this->statusComplain) {
            // Auto approve complaint
            $this->approveComplaint($submitCv);

            // Refund to employer
            $this->processRefund($submitCv);

            // Update status
            $submitCv->update([
                'status_complain' => ($this->statusComplain == 1) ? 3 : 4,
                'status_payment' => 3 // Refunded
            ]);
        }
    }
}
```

#### 3.3.2. Trigger Conditions

-   **Dispatch**: Khi có khiếu nại mới
-   **Delay**: 7 ngày
-   **Purpose**: Tự động chấp nhận khiếu nại nếu không có phản hồi

## 4. Status Transition Jobs

### 4.1. ChangeToTrailWorkSubmit

#### 4.1.1. Job Definition

```php
class ChangeToTrailWorkSubmit implements ShouldQueue
{
    public $submitCvId;
    public $user;

    public function handle()
    {
        $submitCv = SubmitCv::find($this->submitCvId);

        // Chuyển sang Trial work
        $submitCv->update([
            'status' => 14, // Trial work
            'date_change_status' => now()
        ]);

        // Setup payment schedule
        $this->setupPaymentSchedule($submitCv);

        // Setup reminder emails
        $this->setupReminderEmails($submitCv);

        // Setup auto success
        $this->setupAutoSuccess($submitCv);
    }
}
```

#### 4.1.2. Payment Schedule Setup

```php
private function setupPaymentSchedule($submitCv)
{
    $onboard = $submitCv->onboard;
    $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book);

    // 15% sau 30 ngày
    PayOnboardSubmit::dispatch($submitCv->id, 15)
        ->delay($carbonDate->addMinutes(30 * 24 * 60));

    // 10% sau 45 ngày
    PayOnboardSubmit::dispatch($submitCv->id, 10)
        ->delay($carbonDate->addMinutes(45 * 24 * 60));

    // 75% sau 67 ngày
    PayOnboardSubmit::dispatch($submitCv->id, 75)
        ->delay($carbonDate->addMinutes(67 * 24 * 60));
}
```

### 4.2. SuccessRecuitmentSubmit

#### 4.2.1. Job Definition

```php
class SuccessRecuitmentSubmit implements ShouldQueue
{
    public $submitCvId;

    public function handle()
    {
        $submitCv = SubmitCv::find($this->submitCvId);

        // Kiểm tra vẫn ở trial work
        if ($submitCv->status == 14) { // Trial work
            // Auto chuyển thành công
            $submitCv->update([
                'status' => 16, // Success Recruitment
                'date_change_status' => now()
            ]);

            // Send success notifications
            $this->sendSuccessNotifications($submitCv);

            // Final payment processing
            $this->processFinalPayment($submitCv);
        }
    }
}
```

#### 4.2.2. Trigger Conditions

-   **Dispatch**: Khi bắt đầu trial work
-   **Delay**: 67 ngày từ ngày onboard
-   **Purpose**: Tự động chuyển thành công nếu NTD không cập nhật

## 5. Notification Jobs

### 5.1. SendemailBookSubmit

#### 5.1.1. Job Definition

```php
class SendemailBookSubmit implements ShouldQueue
{
    public $submitCv;
    public $user;
    public $book;

    public function handle()
    {
        // Gửi email nhắc nhở trước 1 ngày phỏng vấn
        $this->submitCv->employer->notify(
            new InterviewReminderNotification($this->submitCv, $this->book)
        );

        $this->submitCv->rec->notify(
            new InterviewReminderNotification($this->submitCv, $this->book)
        );
    }
}
```

#### 5.1.2. Trigger Conditions

-   **Dispatch**: Khi đặt lịch phỏng vấn
-   **Delay**: 1 ngày trước ngày phỏng vấn
-   **Purpose**: Nhắc nhở các bên về lịch phỏng vấn

### 5.2. RemindExpireTrailWorkSubmit

#### 5.2.1. Job Definition

```php
class RemindExpireTrailWorkSubmit implements ShouldQueue
{
    public $submitCv;
    public $daysRemaining;

    public function handle()
    {
        // Gửi email nhắc nhở NTD về thời hạn trial work
        $this->submitCv->employer->notify(
            new TrialWorkExpiryReminder($this->submitCv, $this->daysRemaining)
        );
    }
}
```

#### 5.2.2. Reminder Schedule

```php
// Gửi nhắc nhở từ ngày 55-60 của trial work
$start = 55;
$onboard = $submitCv->onboard;
$carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book);

while ($start < 60) {
    $addTime = clone $carbonDate;
    RemindExpireTrailWorkSubmit::dispatch($submitCv, $start)
        ->delay($addTime->addMinutes($start * 24 * 60));
    $start++;
}
```

## 6. Job Monitoring và Error Handling

### 6.1. Job Failure Handling

#### 6.1.1. Retry Logic

```php
class PayInterviewSubmit implements ShouldQueue
{
    public $tries = 3;
    public $backoff = [60, 300, 900]; // 1min, 5min, 15min

    public function failed(Exception $exception)
    {
        // Log failure
        Log::error('PayInterviewSubmit failed', [
            'submit_cv_id' => $this->submitCvId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Notify admin
        Mail::to(config('settings.global.email_admin'))
            ->send(new JobFailureNotification($this, $exception));
    }
}
```

#### 6.1.2. Dead Letter Queue

```php
// Queue configuration
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => env('REDIS_QUEUE', 'default'),
        'retry_after' => 90,
        'block_for' => null,
        'after_commit' => false,
        'failed_jobs' => 'failed_jobs_table'
    ],
]
```

### 6.2. Job Monitoring

#### 6.2.1. Job Status Tracking

```php
class JobStatusTracker
{
    public static function trackJob($jobClass, $parameters, $delay = null)
    {
        JobLog::create([
            'job_class' => $jobClass,
            'parameters' => json_encode($parameters),
            'scheduled_at' => $delay ? now()->add($delay) : now(),
            'status' => 'pending',
            'created_at' => now()
        ]);
    }

    public static function markCompleted($jobId)
    {
        JobLog::where('id', $jobId)->update([
            'status' => 'completed',
            'completed_at' => now()
        ]);
    }
}
```

### 6.3. Performance Optimization

#### 6.3.1. Queue Prioritization

```php
// High priority jobs
PayInterviewSubmit::dispatch($submitCvId)->onQueue('high');
PayOnboardSubmit::dispatch($submitCvId, $percent)->onQueue('high');

// Medium priority jobs
RejectRecruitmentSubmit::dispatch($submitCvId)->onQueue('medium');
SuccessRecuitmentSubmit::dispatch($submitCvId)->onQueue('medium');

// Low priority jobs
SendemailBookSubmit::dispatch($submitCv, $user, $book)->onQueue('low');
RemindExpireTrailWorkSubmit::dispatch($submitCv, $days)->onQueue('low');
```

#### 6.3.2. Batch Processing

```php
// Batch process multiple payments
$paymentJobs = [];
foreach ($submitCvs as $submitCv) {
    $paymentJobs[] = new PayInterviewSubmit($submitCv->id);
}

Bus::batch($paymentJobs)
    ->then(function (Batch $batch) {
        // All jobs completed successfully
        Log::info('Batch payment completed', ['batch_id' => $batch->id]);
    })
    ->catch(function (Batch $batch, Throwable $e) {
        // First batch job failure
        Log::error('Batch payment failed', ['error' => $e->getMessage()]);
    })
    ->dispatch();
```

## 7. Job Dependencies và Ordering

### 7.1. Job Chain Example

```php
// Onboard workflow chain
Chain::of([
    new ChangeToTrailWorkSubmit($submitCvId, $user),
    new PayOnboardSubmit($submitCvId, 15),
    new PayOnboardSubmit($submitCvId, 10),
    new PayOnboardSubmit($submitCvId, 75),
    new SuccessRecuitmentSubmit($submitCvId)
])->dispatch();
```

### 7.2. Conditional Job Execution

```php
public function handle()
{
    $submitCv = SubmitCv::find($this->submitCvId);

    // Chỉ execute nếu điều kiện đúng
    if (!$this->shouldExecute($submitCv)) {
        Log::info('Job skipped due to conditions', [
            'job' => static::class,
            'submit_cv_id' => $this->submitCvId,
            'current_status' => $submitCv->status
        ]);
        return;
    }

    // Execute job logic
    $this->executeJobLogic($submitCv);
}
```

---

**Next**: [05_Submit_CV_API_Endpoints.md](./05_Submit_CV_API_Endpoints.md)
