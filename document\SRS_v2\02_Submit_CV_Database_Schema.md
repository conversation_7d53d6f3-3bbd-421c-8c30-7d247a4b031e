# 02. Submit CV Database Schema

## 1. Tổng quan Database

### 1.1. Core Tables

-   **submit_cvs**: <PERSON><PERSON><PERSON> <PERSON><PERSON>h lưu thông tin submit CV
-   **submit_cvs_history_status**: <PERSON><PERSON><PERSON> sử thay đổi trạng thái
-   **submit_cvs_books**: <PERSON><PERSON><PERSON> phỏng vấn
-   **submit_cvs_onboards**: <PERSON><PERSON><PERSON> onboard
-   **submit_cvs_history_payment**: <PERSON><PERSON><PERSON> sử thanh toán

### 1.2. Relationships Overview

```mermaid
erDiagram
    submit_cvs ||--o{ submit_cvs_history_status : "has many"
    submit_cvs ||--o{ submit_cvs_books : "has many"
    submit_cvs ||--o{ submit_cvs_onboards : "has many"
    submit_cvs ||--o{ submit_cvs_history_payment : "has many"
    submit_cvs }o--|| jobs : "belongs to"
    submit_cvs }o--|| users : "belongs to (CTV)"
    submit_cvs }o--|| companies : "belongs to"
    submit_cvs }o--|| warehouse_cvs : "belongs to"
```

## 2. Bảng submit_cvs (Chính)

### 2.1. DDL Script

```sql
CREATE TABLE `submit_cvs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT 'CTV ID',
  `job_id` bigint(20) DEFAULT NULL,
  `company_id` bigint(20) DEFAULT NULL,
  `candidate_salary_expect` bigint(20) DEFAULT NULL,
  `candidate_currency` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `warehouse_cv_id` bigint(20) DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL COMMENT '0:pending-review, 1:accepted, 2:rejected, 3:pass-interview, 4:fail-interview, 5:onboarded, 6:cancel, 7: draft',
  `status_complain` int(11) DEFAULT 0 COMMENT 'Trạng thái khiếu nại',
  `is_self_apply` tinyint(4) DEFAULT 0 COMMENT '0: Giới thiệu ứng viên, 1: Tự ứng tuyển',
  `expected_date` date DEFAULT NULL,
  `date_change_status` datetime DEFAULT NULL COMMENT 'Ngày thay đổi trạng thái',
  `assessment` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Đánh giá',
  `is_active` tinyint(4) DEFAULT 1 COMMENT '0 inactive 1 active',
  `payment_fee` int(11) DEFAULT NULL COMMENT 'Phí thanh toán',
  `payment_actual` int(11) DEFAULT NULL COMMENT 'Số tiền thực tế',
  `status_payment` tinyint(4) DEFAULT NULL COMMENT 'Trạng thái thanh toán',
  `date_change_status_payment` datetime DEFAULT NULL,
  `interest_rate` int(11) DEFAULT NULL COMMENT 'Lãi suất',
  `interest_money` int(11) DEFAULT NULL COMMENT 'Tiền lãi',
  `rank` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `year_experience` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `candidate_est_timetowork` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `career` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `confirm_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Token xác nhận tuyển dụng',
  `txt_complain` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Nội dung khiếu nại',
  `img_complain` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Hình ảnh khiếu nại',
  `count_complain` int(11) DEFAULT 0,
  `date_complain` datetime DEFAULT NULL,
  `confirm_candidate` datetime DEFAULT NULL COMMENT 'Ngày ứng viên xác nhận',
  `authorize` tinyint(4) DEFAULT 0 COMMENT '0 ko ủy quyền, 1 ủy quyền',
  `authorize_status` tinyint(4) DEFAULT 0 COMMENT '0: Chờ xác nhận, 1: đồng ý, 2: Từ chối',
  `percent_bonus` int(11) DEFAULT 0 COMMENT 'Phần trăm bonus',
  `percent_bonus_value` int(11) DEFAULT 0 COMMENT 'Giá trị bonus',
  `bonus_ntd` int(11) DEFAULT NULL COMMENT 'Bonus từ NTD',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_warehouse_cv_id` (`warehouse_cv_id`),
  KEY `idx_status` (`status`),
  KEY `idx_status_complain` (`status_complain`),
  KEY `idx_confirm_candidate` (`confirm_candidate`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2.2. Column Descriptions

| Column            | Type         | Description          | Values                          |
| ----------------- | ------------ | -------------------- | ------------------------------- |
| `id`              | bigint       | Primary key          | Auto increment                  |
| `code`            | varchar(255) | Mã submit CV         | Generated unique code           |
| `user_id`         | bigint       | ID của CTV           | Foreign key to users            |
| `job_id`          | bigint       | ID công việc         | Foreign key to jobs             |
| `company_id`      | bigint       | ID công ty           | Foreign key to companies        |
| `warehouse_cv_id` | bigint       | ID CV từ kho         | Foreign key to warehouse_cvs    |
| `status`          | tinyint      | Trạng thái chính     | 0-12 (see status mapping)       |
| `status_complain` | int          | Trạng thái khiếu nại | 0-5 (see complain status)       |
| `authorize`       | tinyint      | Ủy quyền             | 0: Không, 1: Có                 |
| `confirm_token`   | varchar      | Token xác nhận       | UUID for candidate confirmation |
| `bonus_ntd`       | int          | Bonus từ NTD         | Amount in VND                   |

### 2.3. Status Mapping

```php
// Main Status (status column)
'submit_cvs_status' => [
    9 => 'Pending confirm',      // Chờ ứng viên xác nhận
    0 => 'Pending review',       // Chờ NTD review
    1 => 'Accepted',            // NTD chấp nhận
    2 => 'Rejected',            // NTD từ chối
    3 => 'Pass-interview',      // Phỏng vấn thành công
    4 => 'Fail-interview',      // Phỏng vấn thất bại
    8 => 'Offering',            // Đang offer
    5 => 'Onboarded',           // Đã onboard
    6 => 'Cancel',              // Hủy
    7 => 'Draft',               // Nháp
    10 => 'Admin review',       // Admin review
    11 => 'Admin rejected',     // Admin từ chối
    12 => 'Candidate rejected', // Ứng viên từ chối
]

// Complain Status (status_complain column)
'status_complain' => [
    0 => 'Không có khiếu nại',
    1 => 'Chờ CTV xác nhận',
    2 => 'Chờ admin xem xét',
    3 => 'Khiếu nại được CTV chấp nhận',
    4 => 'Khiếu nại được Admin chấp nhận',
    5 => 'Khiếu nại không thành công',
]
```

## 3. Bảng submit_cvs_history_status

### 3.1. DDL Script

```sql
CREATE TABLE `submit_cvs_history_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `submit_cvs_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT 'NTD/CTV/ADMIN',
  `type` enum('admin','rec','employer') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `comment` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status_recruitment` int(11) NOT NULL,
  `authority` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_submit_cvs_id` (`submit_cvs_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status_recruitment` (`status_recruitment`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3.2. Purpose

-   Lưu lịch sử tất cả thay đổi trạng thái
-   Audit trail cho compliance
-   Tracking user actions

## 4. Bảng submit_cvs_books (Interview Schedule)

### 4.1. DDL Script

```sql
CREATE TABLE `submit_cvs_books` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `ntd_id` int(11) DEFAULT NULL COMMENT 'NTD',
  `ctv_id` int(11) DEFAULT NULL COMMENT 'CTV',
  `submit_cvs_id` int(11) NOT NULL,
  `date_book` datetime DEFAULT NULL COMMENT 'Ngày phỏng vấn',
  `time_book` time DEFAULT NULL COMMENT 'Giờ phút phỏng vấn',
  `status` int(11) DEFAULT 0 COMMENT '0: vừa đặt, 1: đã xác nhận, 2: bị từ chối',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Địa chỉ phỏng vấn',
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Tên ứng viên',
  `book_time_minute` int(11) DEFAULT NULL COMMENT 'Thời gian phỏng vấn (phút)',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_submit_cvs_id` (`submit_cvs_id`),
  KEY `idx_ntd_id` (`ntd_id`),
  KEY `idx_ctv_id` (`ctv_id`),
  KEY `idx_date_book` (`date_book`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4.2. Business Rules

-   Chỉ NTD đã mua CV mới được đặt lịch
-   CTV phải xác nhận trong 48h
-   Auto-reject nếu không xác nhận

## 5. Bảng submit_cvs_onboards

### 5.1. DDL Script

```sql
CREATE TABLE `submit_cvs_onboards` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `ntd_id` int(11) DEFAULT NULL COMMENT 'NTD',
  `ctv_id` int(11) DEFAULT NULL COMMENT 'CTV',
  `submit_cvs_id` int(11) NOT NULL,
  `date_book` datetime DEFAULT NULL COMMENT 'Ngày onboard',
  `time_book` time DEFAULT NULL COMMENT 'Giờ phút onboard',
  `status` int(11) DEFAULT 0 COMMENT '0: vừa đặt, 1: đã xác nhận, 2: bị từ chối',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Địa chỉ onboard',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Tên ứng viên',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_submit_cvs_id` (`submit_cvs_id`),
  KEY `idx_ntd_id` (`ntd_id`),
  KEY `idx_ctv_id` (`ctv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 6. Bảng submit_cvs_history_payment

### 6.1. DDL Script

```sql
CREATE TABLE `submit_cvs_history_payment` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'NTD',
  `submit_cv_id` int(11) NOT NULL,
  `type` tinyint(4) DEFAULT 0 COMMENT '0 trừ tiền, 1 hoàn tiền',
  `bonus_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `percent` int(11) DEFAULT 0 COMMENT 'Phần trăm thanh toán',
  `comment` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` int(11) NOT NULL COMMENT 'Số tiền',
  `balance` int(11) DEFAULT NULL COMMENT 'Số dư sau thanh toán',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_submit_cv_id` (`submit_cv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 7. Indexes và Performance

### 7.1. Critical Indexes

```sql
-- submit_cvs table
CREATE INDEX idx_user_status ON submit_cvs(user_id, status);
CREATE INDEX idx_job_status ON submit_cvs(job_id, status);
CREATE INDEX idx_company_status ON submit_cvs(company_id, status);
CREATE INDEX idx_status_created ON submit_cvs(status, created_at);

-- submit_cvs_history_status
CREATE INDEX idx_submit_type_created ON submit_cvs_history_status(submit_cvs_id, type, created_at);

-- submit_cvs_books
CREATE INDEX idx_submit_status_date ON submit_cvs_books(submit_cvs_id, status, date_book);
```

### 7.2. Query Optimization

-   Sử dụng composite indexes cho queries phức tạp
-   Partition theo created_at cho large datasets
-   Archive old records để maintain performance

## 8. Sample Data Examples

### 8.1. Submit CV Record

```sql
INSERT INTO submit_cvs (
    code, user_id, job_id, company_id, warehouse_cv_id,
    status, status_complain, authorize, confirm_token,
    bonus_ntd, created_at, updated_at
) VALUES (
    'SUBMIT_CV_20250725_001',
    123,  -- CTV ID
    456,  -- Job ID
    789,  -- Company ID
    101,  -- Warehouse CV ID
    9,    -- Pending confirm
    0,    -- No complaint
    0,    -- No authorization
    'abc123-def456-ghi789',
    5000000, -- 5M VND bonus
    NOW(),
    NOW()
);
```

### 8.2. Status History Record

```sql
INSERT INTO submit_cvs_history_status (
    submit_cvs_id, user_id, type, comment,
    status_recruitment, created_at
) VALUES (
    1,     -- Submit CV ID
    123,   -- User ID (CTV)
    'rec', -- Type: recruiter
    'CTV submit CV cho ứng viên',
    9,     -- Pending confirm status
    NOW()
);
```

### 8.3. Interview Booking Record

```sql
INSERT INTO submit_cvs_books (
    ntd_id, ctv_id, submit_cvs_id,
    date_book, time_book, status,
    address, phone, name, book_time_minute
) VALUES (
    789,   -- NTD ID
    123,   -- CTV ID
    1,     -- Submit CV ID
    '2025-07-30 14:00:00',
    '14:00:00',
    0,     -- Just scheduled
    'Tầng 5, Tòa nhà ABC, Quận 1, TP.HCM',
    '0901234567',
    'Nguyễn Văn A',
    60     -- 60 minutes
);
```

## 9. Foreign Key Constraints

### 9.1. Relationships

```sql
-- submit_cvs foreign keys
ALTER TABLE submit_cvs
ADD CONSTRAINT fk_submit_cvs_user_id
FOREIGN KEY (user_id) REFERENCES users(id);

ALTER TABLE submit_cvs
ADD CONSTRAINT fk_submit_cvs_job_id
FOREIGN KEY (job_id) REFERENCES jobs(id);

ALTER TABLE submit_cvs
ADD CONSTRAINT fk_submit_cvs_company_id
FOREIGN KEY (company_id) REFERENCES companies(id);

ALTER TABLE submit_cvs
ADD CONSTRAINT fk_submit_cvs_warehouse_cv_id
FOREIGN KEY (warehouse_cv_id) REFERENCES warehouse_cvs(id);

-- submit_cvs_history_status foreign keys
ALTER TABLE submit_cvs_history_status
ADD CONSTRAINT fk_history_status_submit_cvs_id
FOREIGN KEY (submit_cvs_id) REFERENCES submit_cvs(id);

-- submit_cvs_books foreign keys
ALTER TABLE submit_cvs_books
ADD CONSTRAINT fk_books_submit_cvs_id
FOREIGN KEY (submit_cvs_id) REFERENCES submit_cvs(id);

-- submit_cvs_onboards foreign keys
ALTER TABLE submit_cvs_onboards
ADD CONSTRAINT fk_onboards_submit_cvs_id
FOREIGN KEY (submit_cvs_id) REFERENCES submit_cvs(id);
```

---

**Next**: [03_Submit_CV_Business_Logic.md](./03_Submit_CV_Business_Logic.md)
