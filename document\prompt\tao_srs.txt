Hãy phân tích toàn bộ codebase của dự án RecLand và viết lại tài liệu Software Requirements Specification (SRS) phiên bản 2.0 với độ chi tiết cao nhất có thể. Lưu tài liệu vào thư mục `document/SRS_v2/`.

**Phạm vi công việc:**
- Tập trung vào luồng Submit CV trước (tạm thời bỏ qua Market CV workflow)
- Phân tích cả backend logic và frontend views để đảm bảo hiểu đúng toàn bộ user experience

**Yêu cầu chi tiết cần bao gồm:**

1. **Business Logic Analysis:**
   - Mô tả chi tiết từng chức năng trong Submit CV workflow
   - Mapping đầy đủ các trạng thái (status) của submit_cv với ID và text description
   - Điều kiện chuyển đổi giữa các trạng thái
   - Business rules và validation logic

2. **Background Jobs Documentation:**
   - Liệt kê tất cả Jobs liên quan đến Submit CV workflow
   - Thời gian delay/schedule của từng job
   - Input/output parameters của mỗi job
   - Trigger conditions và failure handling

3. **Database Schema:**
   - Chi tiết cấu trúc bảng submit_cvs và các bảng liên quan
   - Relationships và foreign keys
   - Indexes và constraints
   - Sample data examples

4. **API Endpoints:**
   - Tất cả endpoints liên quan Submit CV
   - Request/response formats
   - Authentication và authorization requirements
   - Error handling và status codes

5. **Frontend Components:**
   - User interfaces và workflows
   - Form validations và user interactions
   - State management trong Vue.js components

6. **Notification System:**
   - Email templates và triggers
   - In-app notifications
   - SMS notifications (nếu có)

**Mức độ chi tiết yêu cầu:**
Tài liệu phải đủ chi tiết để một developer khác có thể từ đó xây dựng lại toàn bộ Submit CV functionality từ đầu mà không cần tham khảo source code gốc.

**Cấu trúc file output:**
- `00_SRS_v2_Index.md` - Mục lục tổng hợp
- `01_Submit_CV_Overview.md` - Tổng quan Submit CV workflow  
- `02_Submit_CV_Database_Schema.md` - Cấu trúc database
- `03_Submit_CV_Business_Logic.md` - Logic nghiệp vụ chi tiết
- `04_Submit_CV_Jobs_Scheduler.md` - Background jobs và scheduling
- `05_Submit_CV_API_Endpoints.md` - API documentation
- `06_Submit_CV_Frontend_Components.md` - Frontend implementation
- `07_Submit_CV_Notifications.md` - Hệ thống thông báo
- `08_Submit_CV_State_Machine.md` - State transitions và workflows

**Phương pháp thực hiện:**
1. Sử dụng codebase-retrieval để phân tích code liên quan Submit CV
2. Đọc và phân tích Models, Controllers, Services, Jobs, Views
3. Trace through user journeys từ frontend đến backend
4. Document tất cả edge cases và error scenarios
5. Tạo diagrams cho complex workflows sử dụng Mermaid syntax