# Suggested Commands - RecLand

## Development Commands (Windows PowerShell)

### Laravel Artisan Commands
```powershell
# Khởi động development server
php artisan serve

# Chạy migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Seed database
php artisan db:seed

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Generate application key
php artisan key:generate

# Create symbolic link for storage
php artisan storage:link

# Run queue workers
php artisan queue:work

# List all artisan commands
php artisan list
```

### Composer Commands
```powershell
# Install dependencies
composer install

# Update dependencies
composer update

# Dump autoload
composer dump-autoload

# Install specific package
composer require package/name

# Remove package
composer remove package/name
```

### NPM/Asset Commands
```powershell
# Install node dependencies
npm install

# Build assets for development
npm run dev

# Build assets for production
npm run prod

# Watch for changes
npm run watch

# Hot reload
npm run hot
```

### Testing Commands
```powershell
# Run all tests
./vendor/bin/phpunit

# Run specific test
./vendor/bin/phpunit tests/Feature/ExampleTest.php

# Run tests with coverage
./vendor/bin/phpunit --coverage-html coverage
```

### Database Commands
```powershell
# Create new migration
php artisan make:migration create_table_name

# Create model with migration
php artisan make:model ModelName -m

# Create seeder
php artisan make:seeder TableSeeder

# Fresh migrate with seed
php artisan migrate:fresh --seed
```

### Code Generation Commands
```powershell
# Create controller
php artisan make:controller ControllerName

# Create service
php artisan make:service ServiceName

# Create repository
php artisan make:repository RepositoryName

# Create request
php artisan make:request RequestName

# Create job
php artisan make:job JobName

# Create notification
php artisan make:notification NotificationName
```

### Windows Specific Commands
```powershell
# List files
Get-ChildItem
dir

# Change directory
Set-Location path
cd path

# Find files
Get-ChildItem -Recurse -Name "*.php"

# Search in files
Select-String -Path "*.php" -Pattern "search_term"

# Copy files
Copy-Item source destination

# Remove files
Remove-Item file_path
```