# Coding Standards & Conventions - RecLand

## PHP Coding Standards
- <PERSON><PERSON> theo **PSR-12** coding standard
- Sử dụng **4 spaces** cho indentation
- Line length tối đa **120 characters**
- Sử dụng **camelCase** cho methods và properties
- Sử dụng **PascalCase** cho class names

## Laravel Conventions
- **Models**: Singular, PascalCase (User, SubmitCv)
- **Controllers**: PascalCase + Controller (UserController)
- **Services**: PascalCase + Service (UserService)
- **Repositories**: PascalCase + Repository (UserRepository)
- **Migrations**: snake_case với timestamp
- **Routes**: kebab-case
- **Views**: snake_case với dots (user.profile)

## Database Conventions
- **Table names**: snake_case, plural (users, submit_cvs)
- **Column names**: snake_case (created_at, user_id)
- **Foreign keys**: singular_table_id (user_id, company_id)
- **Pivot tables**: alphabetical order (company_user)

## JavaScript/Vue.js Standards
- Sử dụng **ES6+** syntax
- **camelCase** cho variables và functions
- **PascalCase** cho Vue components
- Sử dụng **single quotes** cho strings
- **2 spaces** indentation

## File Organization
```
app/
├── Http/Controllers/
│   ├── Admin/           # Admin controllers
│   ├── Api/            # API controllers
│   └── Frontend/       # Frontend controllers
├── Services/           # Business logic
├── Repositories/       # Data access
└── Models/            # Eloquent models
```

## Documentation Standards
- Sử dụng **PHPDoc** cho PHP classes và methods
- Comment cho complex business logic
- README files cho major features
- API documentation với examples

## Error Handling
- Sử dụng Laravel's exception handling
- Custom exceptions cho business logic
- Proper HTTP status codes
- User-friendly error messages

## Security Practices
- Validate tất cả input data
- Sử dụng Laravel's built-in CSRF protection
- Sanitize output data
- Use prepared statements (Eloquent ORM)
- Proper authentication và authorization

## Performance Guidelines
- Sử dụng Eloquent relationships efficiently
- Implement caching cho expensive operations
- Optimize database queries
- Use queue jobs cho heavy tasks
- Compress và minify assets