# Software Requirements Specification (SRS) v2.0 - Submit CV Workflow

## <PERSON><PERSON><PERSON> lục tổng hợp

### 📋 Thông tin tài liệu
- **<PERSON><PERSON><PERSON> bản**: 2.0
- **<PERSON><PERSON><PERSON> tạ<PERSON>**: 25/07/2025
- **Phạm vi**: Submit CV Workflow (tạm thời bỏ qua Market CV workflow)
- **<PERSON><PERSON><PERSON> đích**: T<PERSON><PERSON> liệu chi tiết đủ để developer kh<PERSON><PERSON> có thể xây dựng lại toàn bộ Submit CV functionality từ đầu

### 📁 Cấu trúc tài liệu

#### [01_Submit_CV_Overview.md](./01_Submit_CV_Overview.md)
**Tổng quan Submit CV workflow**
- High-level architecture
- User journeys và use cases
- Business overview
- Key stakeholders (CTV, NTD, Admin, Candidate)
- Workflow lifecycle overview

#### [02_Submit_CV_Database_Schema.md](./02_Submit_CV_Database_Schema.md)
**Cấu trúc database chi tiết**
- <PERSON>ảng `submit_cvs` - <PERSON><PERSON><PERSON> ch<PERSON><PERSON> lưu thông tin submit CV
- Bảng `submit_cvs_history_status` - Lịch sử thay đổi trạng thái
- Bảng `submit_cvs_books` - Lịch phỏng vấn
- Bảng `submit_cvs_onboards` - Lịch onboard
- Bảng `submit_cvs_history_payment` - Lịch sử thanh toán
- Relationships và foreign keys
- Indexes và constraints
- Sample data examples

#### [03_Submit_CV_Business_Logic.md](./03_Submit_CV_Business_Logic.md)
**Logic nghiệp vụ chi tiết**
- Status definitions và meanings
- Business rules và validation logic
- State transition conditions
- Authorization và permission logic
- Commission calculation rules
- Complaint handling logic
- Payment processing rules

#### [04_Submit_CV_Jobs_Scheduler.md](./04_Submit_CV_Jobs_Scheduler.md)
**Background jobs và scheduling**
- `PayInterviewSubmit` - Thanh toán sau phỏng vấn
- `PayOnboardSubmit` - Thanh toán theo giai đoạn onboard
- `SuccessRecuitmentSubmit` - Tự động chuyển thành công
- `RecSumExpiredPointSubmit` - Xử lý khiếu nại hết hạn
- `RejectRecruitmentSubmit` - Tự động từ chối
- `ChangeToTrailWorkSubmit` - Chuyển sang thử việc
- Delay times và trigger conditions
- Input/output parameters
- Failure handling

#### [05_Submit_CV_API_Endpoints.md](./05_Submit_CV_API_Endpoints.md)
**API documentation**
- Submit CV endpoints
- Status update endpoints
- Interview booking endpoints
- Onboard scheduling endpoints
- File upload endpoints
- Discussion endpoints
- Request/response formats
- Authentication requirements
- Error handling và status codes

#### [06_Submit_CV_Frontend_Components.md](./06_Submit_CV_Frontend_Components.md)
**Frontend implementation**
- Vue.js components
- Blade templates
- JavaScript workflows
- Form validations
- User interactions
- State management
- Modal components
- File upload handling

#### [07_Submit_CV_Notifications.md](./07_Submit_CV_Notifications.md)
**Hệ thống thông báo**
- Email templates và triggers
- In-app notifications
- Notification classes
- Email sending logic
- Notification workflows
- Template variables
- Delivery mechanisms

#### [08_Submit_CV_State_Machine.md](./08_Submit_CV_State_Machine.md)
**State transitions và workflows**
- Submit CV lifecycle
- Status transition diagrams
- Workflow decision points
- Mermaid diagrams
- Edge cases handling
- State validation rules

### 🎯 Mục tiêu chất lượng

#### Độ chi tiết yêu cầu
- **Database**: Đầy đủ DDL scripts, sample data, relationships
- **Business Logic**: Chi tiết từng rule, condition, validation
- **Jobs**: Đầy đủ parameters, timing, failure scenarios
- **API**: Complete request/response examples, error codes
- **Frontend**: Component structure, event handling, state flow
- **Notifications**: Template content, trigger conditions, variables
- **State Machine**: Visual diagrams, transition rules, edge cases

#### Tiêu chuẩn hoàn thành
✅ Developer mới có thể hiểu và implement từ đầu  
✅ Không cần tham khảo source code gốc  
✅ Đầy đủ edge cases và error scenarios  
✅ Có examples và sample data  
✅ Có visual diagrams cho complex workflows  

### 📊 Thống kê tài liệu
- **Tổng số files**: 8 files
- **Phạm vi**: Submit CV workflow only
- **Loại trừ**: Market CV workflow
- **Ngôn ngữ**: Tiếng Việt
- **Format**: Markdown với Mermaid diagrams

### 🔄 Quy trình cập nhật
1. Phân tích source code chi tiết
2. Trace user journeys từ frontend đến backend
3. Document tất cả edge cases
4. Tạo diagrams cho complex workflows
5. Validate với business requirements
6. Review và optimize documentation

---

**Lưu ý**: Tài liệu này được tạo từ phân tích chi tiết source code RecLand và đảm bảo tính chính xác cao nhất có thể. Mọi thay đổi trong source code cần được cập nhật tương ứng trong tài liệu này.
