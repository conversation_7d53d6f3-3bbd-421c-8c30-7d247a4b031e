# Code Structure - RecLand

## Th<PERSON> mục ch<PERSON>h
```
app/
├── Console/Commands/     # Artisan commands
├── DataTables/          # DataTable classes
├── Http/
│   ├── Controllers/
│   │   ├── Admin/       # Admin controllers
│   │   ├── Api/         # API controllers
│   │   └── Frontend/    # Frontend controllers
│   ├── Middleware/      # Custom middleware
│   ├── Requests/        # Form validation
│   └── Resources/       # API resources
├── Models/              # Eloquent models
├── Services/            # Business logic services
├── Repositories/        # Data access layer
├── Jobs/               # Queue jobs
├── Notifications/      # Email notifications
├── Mail/               # Mail classes
└── Traits/             # Reusable traits

config/                 # Configuration files
database/
├── migrations/         # Database migrations
├── seeders/           # Database seeders
└── factories/         # Model factories

resources/
├── js/                # JavaScript files
├── views/             # Blade templates
└── lang/              # Language files

routes/
├── web.php            # Web routes
├── api.php            # API routes
└── admin.php          # Admin routes

public/
├── frontend/          # Frontend assets
└── backend/           # Admin assets
```

## Naming Conventions
- **Models**: PascalCase (User, SubmitCv, WareHouseCv)
- **Controllers**: PascalCase + Controller suffix
- **Services**: PascalCase + Service suffix
- **Repositories**: PascalCase + Repository suffix
- **Variables**: camelCase
- **Database tables**: snake_case
- **Routes**: kebab-case

## Key Models
- **User**: Người dùng hệ thống
- **Company**: Công ty
- **Job**: Công việc
- **SubmitCv**: CV được nộp
- **WareHouseCv**: CV trong kho
- **Transaction**: Giao dịch
- **Wallet**: Ví điện tử